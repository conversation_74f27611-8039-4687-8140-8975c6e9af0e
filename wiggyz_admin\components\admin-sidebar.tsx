"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import {
  BarChart3,
  Users,
  Trophy,
  ShoppingBag,
  Gift,
  Settings,
  Menu,
  Home,
  ChevronDown,
  ChevronRight,
  Target,
  MessageSquare,
  HelpCircle,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { LogoutButton } from "@/components/auth/LogoutButton"

interface SidebarItem {
  title: string
  href: string
  icon: React.ElementType
  submenu?: SidebarItem[]
  isActive?: boolean
}

export function AdminSidebar() {
  const pathname = usePathname()
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const [openSubmenus, setOpenSubmenus] = useState<Record<string, boolean>>({})

  // Define sidebar items
  const sidebarItems: SidebarItem[] = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: Home,
      isActive: pathname === "/dashboard",
    },
    {
      title: "Users",
      href: "/dashboard/users",
      icon: Users,
      isActive: pathname.startsWith("/dashboard/users"),
      submenu: [
        {
          title: "All Users",
          href: "/dashboard/users",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/users",
        },
        {
          title: "Add User",
          href: "/dashboard/users/new",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/users/new",
        },
        {
          title: "Roles",
          href: "/dashboard/users/roles",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/users/roles",
        },
      ],
    },
    {
      title: "Tournaments",
      href: "/dashboard/tournaments",
      icon: Trophy,
      isActive: pathname.startsWith("/dashboard/tournaments"),
      submenu: [
        {
          title: "All Tournaments",
          href: "/dashboard/tournaments",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/tournaments",
        },
        {
          title: "Create Tournament",
          href: "/dashboard/tournaments/create",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/tournaments/create",
        },
        {
          title: "Leaderboards",
          href: "/dashboard/tournaments/leaderboards",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/tournaments/leaderboards",
        },
      ],
    },
    {
      title: "Matches",
      href: "/dashboard/matches",
      icon: Target,
      isActive: pathname.startsWith("/dashboard/matches"),
      submenu: [
        {
          title: "All Matches",
          href: "/dashboard/matches",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/matches",
        },
        {
          title: "Create Match",
          href: "/dashboard/matches/create",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/matches/create",
        },
        {
          title: "Pending Results",
          href: "/dashboard/matches/pending",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/matches/pending",
        },
      ],
    },
    {
      title: "Shop",
      href: "/dashboard/shop",
      icon: ShoppingBag,
      isActive: pathname.startsWith("/dashboard/shop"),
      submenu: [
        {
          title: "Overview",
          href: "/dashboard/shop",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/shop",
        },
        {
          title: "Items",
          href: "/dashboard/shop/items",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/shop/items",
        },
        {
          title: "Games",
          href: "/dashboard/shop/games",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/shop/games",
        },
        {
          title: "Diamonds",
          href: "/dashboard/shop/diamonds",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/shop/diamonds",
        },
        {
          title: "Promotions",
          href: "/dashboard/shop/promotions",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/shop/promotions",
        },
      ],
    },
    {
      title: "Rewards",
      href: "/dashboard/rewards",
      icon: Gift,
      isActive: pathname.startsWith("/dashboard/rewards"),
      submenu: [
        {
          title: "Overview",
          href: "/dashboard/rewards/overview",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/rewards/overview",
        },
        {
          title: "All Rewards",
          href: "/dashboard/rewards",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/rewards" && !pathname.includes("/"),
        },
        {
          title: "Achievements",
          href: "/dashboard/rewards/achievements",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/rewards/achievements",
        },
        {
          title: "Daily Rewards",
          href: "/dashboard/rewards/daily",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/rewards/daily",
        },
        {
          title: "Loyalty Program",
          href: "/dashboard/rewards/loyalty",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/rewards/loyalty",
        },
        {
          title: "Referrals",
          href: "/dashboard/rewards/referrals",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/rewards/referrals",
        },
      ],
    },
    {
      title: "Analytics",
      href: "/dashboard/analytics",
      icon: BarChart3,
      isActive: pathname === "/dashboard/analytics",
    },
    {
      title: "Support",
      href: "/dashboard/support",
      icon: HelpCircle,
      isActive: pathname.startsWith("/dashboard/support"),
      submenu: [
        {
          title: "Messages",
          href: "/dashboard/support/messages",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/support/messages",
        },
        {
          title: "Chat Sessions",
          href: "/dashboard/support/chat",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/support/chat",
        },
        {
          title: "FAQ Management",
          href: "/dashboard/support/faq",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/support/faq",
        },
        {
          title: "Analytics",
          href: "/dashboard/support/analytics",
          icon: ChevronRight,
          isActive: pathname === "/dashboard/support/analytics",
        },
      ],
    },
    {
      title: "Settings",
      href: "/dashboard/settings",
      icon: Settings,
      isActive: pathname === "/dashboard/settings",
    },
    {
      title: "Profile",
      href: "/dashboard/profile",
      icon: Users,
      isActive: pathname === "/dashboard/profile",
    },
  ]

  // Initialize open submenus based on active items
  useEffect(() => {
    const initialOpenSubmenus: Record<string, boolean> = {}
    sidebarItems.forEach((item) => {
      if (item.submenu && item.submenu.some((subItem) => subItem.isActive)) {
        initialOpenSubmenus[item.title] = true
      }
    })
    setOpenSubmenus(initialOpenSubmenus)
  }, [pathname])

  const toggleSubmenu = (title: string) => {
    setOpenSubmenus((prev) => ({
      ...prev,
      [title]: !prev[title],
    }))
  }

  const closeMobileSidebar = () => {
    setIsMobileOpen(false)
  }

  const renderSidebarItems = (items: SidebarItem[]) => {
    return items.map((item) => (
      <div key={item.title} className="mb-1">
        {item.submenu ? (
          <div>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start px-2 py-1.5 h-auto text-sm",
                item.isActive && "bg-accent text-accent-foreground",
              )}
              onClick={() => toggleSubmenu(item.title)}
            >
              <item.icon className="mr-2 h-4 w-4" />
              {item.title}
              <ChevronDown
                className={cn("ml-auto h-4 w-4 transition-transform", openSubmenus[item.title] && "rotate-180")}
              />
            </Button>
            {openSubmenus[item.title] && (
              <div className="ml-4 mt-1 space-y-1 border-l pl-2">
                {item.submenu.map((subItem) => (
                  <Link
                    key={subItem.title}
                    href={subItem.href}
                    onClick={closeMobileSidebar}
                    className={cn(
                      "flex items-center px-2 py-1.5 text-sm rounded-md hover:bg-accent hover:text-accent-foreground",
                      subItem.isActive && "bg-accent text-accent-foreground",
                    )}
                  >
                    <subItem.icon className="mr-2 h-3 w-3" />
                    {subItem.title}
                  </Link>
                ))}
              </div>
            )}
          </div>
        ) : (
          <Link href={item.href} onClick={closeMobileSidebar}>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start px-2 py-1.5 h-auto text-sm",
                item.isActive && "bg-accent text-accent-foreground",
              )}
            >
              <item.icon className="mr-2 h-4 w-4" />
              {item.title}
            </Button>
          </Link>
        )}
      </div>
    ))
  }

  // Desktop sidebar
  const DesktopSidebar = (
    <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 z-10 pt-16 bg-background border-r">
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-1">{renderSidebarItems(sidebarItems)}</div>
        <div className="px-2 pt-4 mt-6 border-t">
          <LogoutButton />
        </div>
      </ScrollArea>
    </div>
  )

  // Mobile sidebar
  const MobileSidebar = (
    <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden fixed bottom-4 right-4 z-50 rounded-full shadow-lg bg-primary text-primary-foreground"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[80%] max-w-[300px] pt-10">
        <ScrollArea className="h-[calc(100vh-4rem)]">
          <div className="space-y-1 px-2 py-4">{renderSidebarItems(sidebarItems)}</div>
          <div className="px-2 pt-4 mt-6 border-t">
            <LogoutButton />
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  )

  return (
    <>
      {DesktopSidebar}
      {MobileSidebar}
    </>
  )
}
