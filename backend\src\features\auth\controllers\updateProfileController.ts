import { Request, Response } from 'express';
import { supabase } from '../../../config/supabase';

export const updateUserProfile = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.userId;
    const {
      username,
      name,
      email,
      phone,
      bio,
      location,
      dateOfBirth,
      country,
      // Free Fire fields
      ff_name,
      ff_uid,
      ff_level,
      ff_server,
      ff_rank,
      ff_preferred_mode,
      // PUBG fields
      pubg_name,
      pubg_uid,
      pubg_level,
      pubg_server,
      pubg_rank,
      pubg_preferred_mode
    } = req.body;

    // Prepare update object with only the fields that are provided
    const updateData: Record<string, any> = {};
    if (username !== undefined) updateData.name = username; // Map username to name field
    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (bio !== undefined) updateData.bio = bio;
    if (location !== undefined) updateData.location = location;
    if (dateOfBirth !== undefined) updateData.date_of_birth = dateOfBirth;
    if (country !== undefined) updateData.country = country;

    // Free Fire fields
    if (ff_name !== undefined) updateData.ff_name = ff_name;
    if (ff_uid !== undefined) updateData.ff_uid = ff_uid;
    if (ff_level !== undefined) updateData.ff_level = ff_level;
    if (ff_server !== undefined) updateData.ff_server = ff_server;
    if (ff_rank !== undefined) updateData.ff_rank = ff_rank;
    if (ff_preferred_mode !== undefined) updateData.ff_preferred_mode = ff_preferred_mode;

    // PUBG fields
    if (pubg_name !== undefined) updateData.pubg_name = pubg_name;
    if (pubg_uid !== undefined) updateData.pubg_uid = pubg_uid;
    if (pubg_level !== undefined) updateData.pubg_level = pubg_level;
    if (pubg_server !== undefined) updateData.pubg_server = pubg_server;
    if (pubg_rank !== undefined) updateData.pubg_rank = pubg_rank;
    if (pubg_preferred_mode !== undefined) updateData.pubg_preferred_mode = pubg_preferred_mode;

    // Always update the timestamp
    updateData.updated_at = new Date();

    // Update user profile in Supabase
    const { error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId);

    if (error) {
      return res.status(400).json({ error: error.message });
    }

    // Get the updated profile
    const { data: updatedProfile, error: fetchError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        name,
        phone,
        country,
        bio,
        location,
        profile_image,
        profile_image_url,
        date_of_birth,
        ff_name,
        ff_uid,
        ff_level,
        ff_server,
        ff_rank,
        ff_preferred_mode,
        pubg_name,
        pubg_uid,
        pubg_level,
        pubg_server,
        pubg_rank,
        pubg_preferred_mode,
        wallet_balance,
        reward_points,
        honor_score,
        is_verified,
        role,
        created_at,
        updated_at,
        last_active
      `)
      .eq('id', userId)
      .single();

    if (fetchError) {
      return res.status(400).json({ error: fetchError.message });
    }

    return res.status(200).json({
      message: 'Profile updated successfully',
      user: updatedProfile
    });
  } catch (err) {
    console.error('Profile update error:', err);
    return res.status(500).json({ error: 'Internal server error' });
  }
};
