/**
 * Games Routes
 * Defines routes for games-related endpoints
 */

import { Router } from 'express';
import {
  getAllGames,
  getFeaturedGames,
  getPopularGames,
  getGamesByCategory,
  getGameById,
  searchGames,
  getGameCategories,
  createGame,
  updateGame,
  deleteGame,
  updatePlayerCounts
} from '../features/games/controllers/gamesController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Public routes (no authentication required)
router.get('/', getAllGames);
router.get('/featured', getFeaturedGames);
router.get('/popular', getPopularGames);
router.get('/categories', getGameCategories);
router.get('/search', searchGames);
router.get('/category/:category', getGamesByCategory);
router.get('/:gameId', getGameById);

// Protected routes (authentication required)
router.post('/', authenticate, createGame);
router.put('/:gameId', authenticate, updateGame);
router.delete('/:gameId', authenticate, deleteGame);
router.post('/update-player-counts', authenticate, updatePlayerCounts);

export default router;
