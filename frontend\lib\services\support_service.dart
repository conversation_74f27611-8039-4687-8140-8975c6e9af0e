import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:wiggyz_app/core/api/api_config.dart';
import 'package:wiggyz_app/models/faq_model.dart';
import 'package:wiggyz_app/models/support_message_model.dart';
import 'package:wiggyz_app/models/chat_model.dart';
import 'package:wiggyz_app/services/auth_service.dart';

class SupportService {
  final AuthService _authService = AuthService();
  String get _baseUrl => '${ApiConfig.baseUrl}/support';

  // FAQ Methods
  Future<List<FaqCategory>> getFaqCategories() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/faq/categories'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> categoriesJson = data['data'] ?? [];
        return categoriesJson
            .map((json) => FaqCategory.fromJson(json))
            .toList();
      } else {
        throw Exception(
          'Failed to load FAQ categories: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Error loading FAQ categories: $e');
    }
  }

  Future<List<FaqItem>> getFaqItems() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/faq/items'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> itemsJson = data['data'] ?? [];
        return itemsJson.map((json) => FaqItem.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load FAQ items: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading FAQ items: $e');
    }
  }

  Future<List<FaqItem>> getFaqItemsByCategory(String categoryId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/faq/categories/$categoryId/items'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> itemsJson = data['data'] ?? [];
        return itemsJson.map((json) => FaqItem.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load FAQ items: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading FAQ items: $e');
    }
  }

  Future<void> submitFaqFeedback(
    String faqItemId,
    bool isHelpful, {
    String? feedbackText,
  }) async {
    try {
      final token = await _getAuthToken();
      final response = await http.post(
        Uri.parse('$_baseUrl/faq/items/$faqItemId/feedback'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'is_helpful': isHelpful,
          if (feedbackText != null) 'feedback_text': feedbackText,
        }),
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to submit feedback: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error submitting feedback: $e');
    }
  }

  // Support Message Methods
  Future<SupportMessage> createSupportMessage({
    required String subject,
    required String category,
    required String message,
  }) async {
    try {
      final token = await _getAuthToken();
      final response = await http.post(
        Uri.parse('$_baseUrl/messages'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'subject': subject,
          'category': category,
          'message': message,
        }),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return SupportMessage.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['error'] ?? 'Failed to create support message',
        );
      }
    } catch (e) {
      throw Exception('Error creating support message: $e');
    }
  }

  Future<List<SupportMessage>> getUserSupportMessages({
    String? status,
    String? category,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null) queryParams['status'] = status;
      if (category != null) queryParams['category'] = category;

      final uri = Uri.parse(
        '$_baseUrl/messages',
      ).replace(queryParameters: queryParams);

      final token = await _getAuthToken();
      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> messagesJson = data['data']['messages'] ?? [];
        return messagesJson
            .map((json) => SupportMessage.fromJson(json))
            .toList();
      } else {
        throw Exception(
          'Failed to load support messages: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Error loading support messages: $e');
    }
  }

  Future<SupportMessage?> getSupportMessageById(String messageId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('$_baseUrl/messages/$messageId'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return SupportMessage.fromJson(data['data']);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception(
          'Failed to load support message: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Error loading support message: $e');
    }
  }

  // Chat Methods
  Future<ChatSession> createChatSession({
    String sessionType = 'support',
  }) async {
    try {
      final token = await _getAuthToken();
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/sessions'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode({'session_type': sessionType}),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return ChatSession.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to create chat session');
      }
    } catch (e) {
      throw Exception('Error creating chat session: $e');
    }
  }

  Future<List<ChatSession>> getUserChatSessions({
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null) queryParams['status'] = status;

      final uri = Uri.parse(
        '$_baseUrl/chat/sessions',
      ).replace(queryParameters: queryParams);

      final token = await _getAuthToken();
      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> sessionsJson = data['data']['sessions'] ?? [];
        return sessionsJson.map((json) => ChatSession.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load chat sessions: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading chat sessions: $e');
    }
  }

  Future<ChatMessage> sendChatMessage({
    required String sessionId,
    required String messageText,
    String messageType = 'text',
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final token = await _getAuthToken();
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'message_text': messageText,
          'message_type': messageType,
          'metadata': metadata ?? {},
        }),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return ChatMessage.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to send message');
      }
    } catch (e) {
      throw Exception('Error sending message: $e');
    }
  }

  Future<List<ChatMessage>> getChatMessages(String sessionId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> messagesJson = data['data'] ?? [];
        return messagesJson.map((json) => ChatMessage.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load chat messages: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading chat messages: $e');
    }
  }

  Future<ChatSession> closeChatSession(String sessionId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/close'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ChatSession.fromJson(data['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to close chat session');
      }
    } catch (e) {
      throw Exception('Error closing chat session: $e');
    }
  }

  // Helper method to get auth token
  Future<String?> _getAuthToken() async {
    return await _authService.getToken();
  }
}
