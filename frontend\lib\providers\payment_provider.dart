import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:wiggyz_app/screens/payment_settings_screen.dart';

class PaymentProvider extends ChangeNotifier {
  static const String _paymentMethodsKey = 'saved_payment_methods';
  static const String _defaultPaymentMethodKey = 'default_payment_method_id';

  // State variables
  List<PaymentMethod> _paymentMethods = [];
  String? _defaultPaymentMethodId;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<PaymentMethod> get paymentMethods => _paymentMethods;
  String? get defaultPaymentMethodId => _defaultPaymentMethodId;
  bool get isLoading => _isLoading;
  String? get error => _error;

  PaymentMethod? get defaultPaymentMethod {
    if (_defaultPaymentMethodId == null) return null;
    try {
      return _paymentMethods.firstWhere(
        (method) => method.id == _defaultPaymentMethodId,
      );
    } catch (e) {
      return null;
    }
  }

  bool get hasPaymentMethods => _paymentMethods.isNotEmpty;

  // Initialize provider by loading saved data
  Future<void> init() async {
    await loadPaymentMethods();
  }

  // Load payment methods from SharedPreferences
  Future<void> loadPaymentMethods() async {
    _setLoading(true);
    _clearError();

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load payment methods
      final paymentMethodsJson = prefs.getString(_paymentMethodsKey);
      if (paymentMethodsJson != null) {
        final List<dynamic> paymentMethodsList = json.decode(paymentMethodsJson);
        _paymentMethods = paymentMethodsList
            .map((json) => PaymentMethod.fromJson(json))
            .toList();
      } else {
        _paymentMethods = [];
      }

      // Load default payment method ID
      _defaultPaymentMethodId = prefs.getString(_defaultPaymentMethodKey);

      // Validate default payment method still exists
      if (_defaultPaymentMethodId != null) {
        final defaultExists = _paymentMethods.any(
          (method) => method.id == _defaultPaymentMethodId,
        );
        if (!defaultExists) {
          _defaultPaymentMethodId = null;
          await _saveDefaultPaymentMethodId();
        }
      }

      // If no default is set but we have payment methods, set the first one as default
      if (_defaultPaymentMethodId == null && _paymentMethods.isNotEmpty) {
        _defaultPaymentMethodId = _paymentMethods.first.id;
        await _saveDefaultPaymentMethodId();
      }

    } catch (e) {
      _setError('Failed to load payment methods: $e');
      debugPrint('Error loading payment methods: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Save payment methods to SharedPreferences
  Future<void> _savePaymentMethods() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final paymentMethodsJson = json.encode(
        _paymentMethods.map((method) => method.toJson()).toList(),
      );
      await prefs.setString(_paymentMethodsKey, paymentMethodsJson);
    } catch (e) {
      debugPrint('Error saving payment methods: $e');
      throw Exception('Failed to save payment methods');
    }
  }

  // Save default payment method ID
  Future<void> _saveDefaultPaymentMethodId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_defaultPaymentMethodId != null) {
        await prefs.setString(_defaultPaymentMethodKey, _defaultPaymentMethodId!);
      } else {
        await prefs.remove(_defaultPaymentMethodKey);
      }
    } catch (e) {
      debugPrint('Error saving default payment method ID: $e');
    }
  }

  // Add a new payment method
  Future<bool> addPaymentMethod(PaymentMethod paymentMethod) async {
    _setLoading(true);
    _clearError();

    try {
      // Generate unique ID for the payment method
      final newMethod = paymentMethod.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
      );

      _paymentMethods.add(newMethod);

      // If this is the first payment method, set it as default
      if (_paymentMethods.length == 1) {
        _defaultPaymentMethodId = newMethod.id;
        await _saveDefaultPaymentMethodId();
      }

      await _savePaymentMethods();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add payment method: $e');
      debugPrint('Error adding payment method: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update an existing payment method
  Future<bool> updatePaymentMethod(PaymentMethod updatedMethod) async {
    _setLoading(true);
    _clearError();

    try {
      final index = _paymentMethods.indexWhere(
        (method) => method.id == updatedMethod.id,
      );

      if (index == -1) {
        throw Exception('Payment method not found');
      }

      _paymentMethods[index] = updatedMethod;
      await _savePaymentMethods();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update payment method: $e');
      debugPrint('Error updating payment method: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete a payment method
  Future<bool> deletePaymentMethod(String paymentMethodId) async {
    _setLoading(true);
    _clearError();

    try {
      final index = _paymentMethods.indexWhere(
        (method) => method.id == paymentMethodId,
      );

      if (index == -1) {
        throw Exception('Payment method not found');
      }

      _paymentMethods.removeAt(index);

      // If the deleted method was the default, set a new default
      if (_defaultPaymentMethodId == paymentMethodId) {
        _defaultPaymentMethodId = _paymentMethods.isNotEmpty 
            ? _paymentMethods.first.id 
            : null;
        await _saveDefaultPaymentMethodId();
      }

      await _savePaymentMethods();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete payment method: $e');
      debugPrint('Error deleting payment method: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Set a payment method as default
  Future<bool> setDefaultPaymentMethod(String paymentMethodId) async {
    _setLoading(true);
    _clearError();

    try {
      final methodExists = _paymentMethods.any(
        (method) => method.id == paymentMethodId,
      );

      if (!methodExists) {
        throw Exception('Payment method not found');
      }

      _defaultPaymentMethodId = paymentMethodId;
      await _saveDefaultPaymentMethodId();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to set default payment method: $e');
      debugPrint('Error setting default payment method: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Clear all payment methods
  Future<void> clearAllPaymentMethods() async {
    _setLoading(true);
    _clearError();

    try {
      _paymentMethods.clear();
      _defaultPaymentMethodId = null;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_paymentMethodsKey);
      await prefs.remove(_defaultPaymentMethodKey);
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear payment methods: $e');
      debugPrint('Error clearing payment methods: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get payment methods by type
  List<PaymentMethod> getPaymentMethodsByType(PaymentType type) {
    return _paymentMethods.where((method) => method.type == type).toList();
  }

  // Check if a payment type is already added
  bool hasPaymentMethodOfType(PaymentType type) {
    return _paymentMethods.any((method) => method.type == type);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Clear error state
  void clearError() {
    _clearError();
    notifyListeners();
  }
}
