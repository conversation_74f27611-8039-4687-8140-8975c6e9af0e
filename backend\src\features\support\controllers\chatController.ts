/**
 * Chat Controller
 * Handles real-time chat functionality for support
 */

import { Request, Response } from 'express';
import { chatService } from '../services/chatService';
import { errorHandler } from '../../../utils/errorHandler';
import { logger } from '../../../utils/logger';

/**
 * Create a new chat session
 */
export const createChatSession = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { session_type } = req.body;

    const chatSession = await chatService.createChatSession({
      userId,
      sessionType: session_type || 'support'
    });

    logger.info(`Chat session created: ${chatSession.id} by user ${userId}`);

    res.status(201).json({
      message: 'Chat session created successfully',
      data: chatSession
    });
  } catch (error) {
    logger.error(`<PERSON>rror creating chat session: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get user's chat sessions
 */
export const getUserChatSessions = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const filters = req.query;
    const sessions = await chatService.getUserChatSessions(userId, filters);

    res.json({
      message: 'Chat sessions retrieved successfully',
      data: sessions
    });
  } catch (error) {
    logger.error(`Error getting user chat sessions: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get all chat sessions (admin only)
 */
export const getAllChatSessions = async (req: Request, res: Response) => {
  try {
    const filters = req.query;
    const sessions = await chatService.getAllChatSessions(filters);

    res.json({
      message: 'All chat sessions retrieved successfully',
      data: sessions
    });
  } catch (error) {
    logger.error(`Error getting all chat sessions: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get chat session by ID
 */
export const getChatSessionById = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user?.userId;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const session = await chatService.getChatSessionById(sessionId, userId, userRole);

    if (!session) {
      return res.status(404).json({ error: 'Chat session not found' });
    }

    res.json({
      message: 'Chat session retrieved successfully',
      data: session
    });
  } catch (error) {
    logger.error(`Error getting chat session by ID: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Send chat message
 */
export const sendChatMessage = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user?.userId;
    const { message_text, message_type, metadata } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const message = await chatService.sendChatMessage({
      sessionId,
      senderId: userId,
      senderType: 'user',
      messageText: message_text,
      messageType: message_type || 'text',
      metadata: metadata || {}
    });

    logger.info(`Chat message sent: ${message.id} in session ${sessionId}`);

    res.status(201).json({
      message: 'Chat message sent successfully',
      data: message
    });
  } catch (error) {
    logger.error(`Error sending chat message: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Send admin chat message
 */
export const sendAdminChatMessage = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const adminUserId = req.user?.userId;
    const { message_text, message_type, metadata } = req.body;

    if (!adminUserId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const message = await chatService.sendChatMessage({
      sessionId,
      senderId: adminUserId,
      senderType: 'admin',
      messageText: message_text,
      messageType: message_type || 'text',
      metadata: metadata || {}
    });

    logger.info(`Admin chat message sent: ${message.id} in session ${sessionId}`);

    res.status(201).json({
      message: 'Admin chat message sent successfully',
      data: message
    });
  } catch (error) {
    logger.error(`Error sending admin chat message: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get chat messages
 */
export const getChatMessages = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user?.userId;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const messages = await chatService.getChatMessages(sessionId, userId, userRole);

    res.json({
      message: 'Chat messages retrieved successfully',
      data: messages
    });
  } catch (error) {
    logger.error(`Error getting chat messages: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Close chat session
 */
export const closeChatSession = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user?.userId;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const session = await chatService.closeChatSession(sessionId, userId, userRole);

    if (!session) {
      return res.status(404).json({ error: 'Chat session not found' });
    }

    logger.info(`Chat session closed: ${sessionId} by user ${userId}`);

    res.json({
      message: 'Chat session closed successfully',
      data: session
    });
  } catch (error) {
    logger.error(`Error closing chat session: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Assign chat session to admin
 */
export const assignChatSession = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const { admin_user_id } = req.body;
    const adminUserId = req.user?.userId;

    if (!adminUserId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const session = await chatService.assignChatSession(sessionId, admin_user_id, adminUserId);

    if (!session) {
      return res.status(404).json({ error: 'Chat session not found' });
    }

    logger.info(`Chat session assigned: ${sessionId} to ${admin_user_id} by ${adminUserId}`);

    res.json({
      message: 'Chat session assigned successfully',
      data: session
    });
  } catch (error) {
    logger.error(`Error assigning chat session: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Transfer chat session to another admin
 */
export const transferChatSession = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const { new_admin_id } = req.body;
    const adminUserId = req.user?.userId;

    if (!adminUserId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const session = await chatService.transferChatSession(sessionId, new_admin_id, adminUserId);

    if (!session) {
      return res.status(404).json({ error: 'Chat session not found' });
    }

    logger.info(`Chat session transferred: ${sessionId} to ${new_admin_id} by ${adminUserId}`);

    res.json({
      message: 'Chat session transferred successfully',
      data: session
    });
  } catch (error) {
    logger.error(`Error transferring chat session: ${error}`);
    return errorHandler(error as Error, res);
  }
};
