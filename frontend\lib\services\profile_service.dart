import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../core/api/api_config.dart';
import 'auth_service.dart';

/// Service to handle all profile-related API calls
class ProfileService {
  final AuthService _authService;

  // Base URL for API endpoints
  final String _apiBaseUrl =
      kReleaseMode
          ? 'https://api.wiggyz.com/api/v1' // Production URL
          : ApiConfig.baseUrl; // Development URL

  ProfileService(this._authService);

  /// Get the current user's profile from the API
  Future<Map<String, dynamic>?> getUserProfile({int retryCount = 0}) async {
    const maxRetries = 2;

    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('Cannot fetch profile: No authentication token');
        return null;
      }

      final url = '$_apiBaseUrl/profile';
      debugPrint('Fetching profile from: $url (attempt ${retryCount + 1})');
      debugPrint('Using token: ${token.substring(0, 20)}...');

      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
            },
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw Exception('Profile request timed out');
            },
          );

      debugPrint('Profile API response: ${response.statusCode}');
      debugPrint('Profile API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['profile'] ?? data;
      } else if (response.statusCode == 401) {
        debugPrint('Received 401 Unauthorized - attempting token refresh');

        // Token expired - attempt to refresh
        try {
          final refreshed = await _authService.refreshToken();
          if (refreshed && retryCount < maxRetries) {
            debugPrint('Token refresh successful, retrying profile request');
            // Retry with new token
            return await getUserProfile(retryCount: retryCount + 1);
          } else if (!refreshed) {
            debugPrint('Token refresh failed - user needs to re-authenticate');
            return null;
          } else {
            debugPrint('Max retries reached after token refresh');
            return null;
          }
        } catch (refreshError) {
          debugPrint('Error during token refresh: $refreshError');
          return null;
        }
      } else {
        debugPrint(
          'Failed to get profile: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      debugPrint('Error fetching user profile: $e');

      // Retry on network errors if we haven't exceeded max retries
      if (retryCount < maxRetries &&
          (e.toString().contains('timeout') ||
              e.toString().contains('connection') ||
              e.toString().contains('network'))) {
        debugPrint('Network error detected, retrying in 2 seconds...');
        await Future.delayed(const Duration(seconds: 2));
        return await getUserProfile(retryCount: retryCount + 1);
      }

      return null;
    }
  }

  /// Update the user's profile information
  Future<bool> updateProfile({
    String? username,
    String? email,
    String? phone,
    String? bio,
    String? location,
    String? dateOfBirth,
    // Free Fire fields
    String? ffName,
    String? ffUid,
    int? ffLevel,
    String? ffServer,
    String? ffRank,
    String? ffPreferredMode,
    // PUBG fields
    String? pubgName,
    String? pubgUid,
    int? pubgLevel,
    String? pubgServer,
    String? pubgRank,
    String? pubgPreferredMode,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('Cannot update profile: No authentication token');
        return false;
      }

      // Build request body with only non-null values
      final Map<String, dynamic> requestBody = {};
      if (username != null) requestBody['username'] = username;
      if (email != null) requestBody['email'] = email;
      if (phone != null) requestBody['phone'] = phone;
      if (bio != null) requestBody['bio'] = bio;
      if (location != null) requestBody['location'] = location;
      if (dateOfBirth != null) requestBody['dateOfBirth'] = dateOfBirth;

      // Free Fire fields
      if (ffName != null) requestBody['ff_name'] = ffName;
      if (ffUid != null) requestBody['ff_uid'] = ffUid;
      if (ffLevel != null) requestBody['ff_level'] = ffLevel;
      if (ffServer != null) requestBody['ff_server'] = ffServer;
      if (ffRank != null) requestBody['ff_rank'] = ffRank;
      if (ffPreferredMode != null) {
        requestBody['ff_preferred_mode'] = ffPreferredMode;
      }

      // PUBG fields
      if (pubgName != null) requestBody['pubg_name'] = pubgName;
      if (pubgUid != null) requestBody['pubg_uid'] = pubgUid;
      if (pubgLevel != null) requestBody['pubg_level'] = pubgLevel;
      if (pubgServer != null) requestBody['pubg_server'] = pubgServer;
      if (pubgRank != null) requestBody['pubg_rank'] = pubgRank;
      if (pubgPreferredMode != null) {
        requestBody['pubg_preferred_mode'] = pubgPreferredMode;
      }

      final response = await http.put(
        Uri.parse('$_apiBaseUrl/profile'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        return true;
      } else if (response.statusCode == 401) {
        // Token expired - attempt to refresh
        final refreshed = await _authService.refreshToken();
        if (refreshed) {
          // Retry with new token
          return await updateProfile(
            username: username,
            email: email,
            phone: phone,
            bio: bio,
            location: location,
            dateOfBirth: dateOfBirth,
            ffName: ffName,
            ffUid: ffUid,
            ffLevel: ffLevel,
            ffServer: ffServer,
            ffRank: ffRank,
            ffPreferredMode: ffPreferredMode,
            pubgName: pubgName,
            pubgUid: pubgUid,
            pubgLevel: pubgLevel,
            pubgServer: pubgServer,
            pubgRank: pubgRank,
            pubgPreferredMode: pubgPreferredMode,
          );
        }
        return false;
      } else {
        debugPrint('Failed to update profile: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return false;
    }
  }

  /// Upload a profile image to the server
  Future<String?> uploadProfileImage(File imageFile) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('Cannot upload image: No authentication token');
        return null;
      }

      // Create a multipart request
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_apiBaseUrl/profile/image'),
      );

      // Set headers
      request.headers['Authorization'] = 'Bearer $token';

      // Add file to request
      request.files.add(
        await http.MultipartFile.fromPath('image', imageFile.path),
      );

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['imageUrl'];
      } else if (response.statusCode == 401) {
        // Token expired - attempt to refresh
        final refreshed = await _authService.refreshToken();
        if (refreshed) {
          // Retry with new token
          return await uploadProfileImage(imageFile);
        }
        return null;
      } else {
        debugPrint('Failed to upload profile image: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error uploading profile image: $e');
      return null;
    }
  }

  /// Get user achievements from the API
  Future<List<Map<String, dynamic>>?> getUserAchievements() async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('Cannot fetch achievements: No authentication token');
        return null;
      }

      final response = await http.get(
        Uri.parse('$_apiBaseUrl/profile/achievements'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['achievements'] ?? []);
      } else if (response.statusCode == 401) {
        // Token expired - attempt to refresh
        final refreshed = await _authService.refreshToken();
        if (refreshed) {
          // Retry with new token
          return await getUserAchievements();
        }
        return null;
      } else {
        debugPrint('Failed to get achievements: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error fetching user achievements: $e');
      return null;
    }
  }

  /// Get user activity from the API
  Future<List<Map<String, dynamic>>?> getUserActivity() async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('Cannot fetch activity: No authentication token');
        return null;
      }

      final response = await http.get(
        Uri.parse('$_apiBaseUrl/profile/activity'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['activities'] ?? []);
      } else if (response.statusCode == 401) {
        // Token expired - attempt to refresh
        final refreshed = await _authService.refreshToken();
        if (refreshed) {
          // Retry with new token
          return await getUserActivity();
        }
        return null;
      } else {
        debugPrint('Failed to get activity: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error fetching user activity: $e');
      return null;
    }
  }

  /// Add a new activity for the user
  Future<bool> addActivity(
    String activityType,
    Map<String, dynamic> activityData,
  ) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('Cannot add activity: No authentication token');
        return false;
      }

      final response = await http.post(
        Uri.parse('$_apiBaseUrl/profile/activity'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({'type': activityType, 'data': activityData}),
      );

      if (response.statusCode == 201) {
        return true;
      } else if (response.statusCode == 401) {
        // Token expired - attempt to refresh
        final refreshed = await _authService.refreshToken();
        if (refreshed) {
          // Retry with new token
          return await addActivity(activityType, activityData);
        }
        return false;
      } else {
        debugPrint('Failed to add activity: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error adding user activity: $e');
      return false;
    }
  }
}
