#!/bin/bash

# Support System Test Runner
# Runs comprehensive tests for the WiggyZ support system

echo "🚀 Starting WiggyZ Support System Tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

# Check if backend is running
print_status "Checking if backend is running..."
if ! curl -s http://localhost:8000/health > /dev/null; then
    print_warning "Backend is not running on localhost:8000"
    print_status "Please start the backend server before running tests"
    exit 1
fi

print_success "Backend is running"

# Get dependencies
print_status "Getting Flutter dependencies..."
flutter pub get

if [ $? -ne 0 ]; then
    print_error "Failed to get Flutter dependencies"
    exit 1
fi

print_success "Dependencies installed"

# Run unit tests first
print_status "Running unit tests..."
flutter test test/

if [ $? -ne 0 ]; then
    print_error "Unit tests failed"
    exit 1
fi

print_success "Unit tests passed"

# Run widget tests
print_status "Running widget tests..."
flutter test test/screens/ test/widgets/

if [ $? -ne 0 ]; then
    print_warning "Some widget tests failed, but continuing with integration tests"
fi

# Run integration tests
print_status "Running support system integration tests..."
flutter test integration_test/support_system_test.dart

if [ $? -ne 0 ]; then
    print_error "Integration tests failed"
    exit 1
fi

print_success "Integration tests passed"

# Generate test coverage report
print_status "Generating test coverage report..."
flutter test --coverage

if [ $? -eq 0 ]; then
    print_success "Coverage report generated in coverage/lcov.info"
    
    # Convert to HTML if lcov is available
    if command -v genhtml &> /dev/null; then
        print_status "Converting coverage to HTML..."
        genhtml coverage/lcov.info -o coverage/html
        print_success "HTML coverage report generated in coverage/html/"
    else
        print_warning "genhtml not found. Install lcov to generate HTML coverage reports"
    fi
else
    print_warning "Failed to generate coverage report"
fi

# Run performance tests (if any)
print_status "Running performance tests..."
flutter drive --target=test_driver/support_system_perf.dart 2>/dev/null

if [ $? -eq 0 ]; then
    print_success "Performance tests passed"
else
    print_warning "Performance tests not found or failed"
fi

# Summary
echo ""
echo "📊 Test Summary:"
echo "=================="
print_success "✅ Unit tests: PASSED"
print_success "✅ Integration tests: PASSED"
print_success "✅ Support system functionality verified"

echo ""
echo "🎉 All support system tests completed successfully!"
echo ""
echo "📋 Next steps:"
echo "- Review test coverage report in coverage/html/"
echo "- Check integration test results for any warnings"
echo "- Verify support system functionality in staging environment"
echo ""

exit 0
