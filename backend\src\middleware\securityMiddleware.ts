/**
 * Security Middleware
 * Provides comprehensive security features for the support system
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { InputSanitizer } from '../utils/inputSanitizer';
import { logger } from '../utils/logger';
import { supabase } from '../config/supabase';

/**
 * Content Security Policy middleware
 */
export const contentSecurityPolicy = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.wiggyz.com"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
});

/**
 * Rate limiting configurations for different endpoints
 */
export const createRateLimit = (options: {
  windowMs: number;
  max: number;
  message: string;
  skipSuccessfulRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: {
      error: 'Rate limit exceeded',
      message: options.message,
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
      res.status(429).json({
        error: 'Rate limit exceeded',
        message: options.message,
      });
    },
  });
};

/**
 * Support-specific rate limits
 */
export const supportMessageRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 messages per hour
  message: 'Too many support messages. Please wait before sending another message.',
});

export const chatMessageRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 messages per minute
  message: 'Too many chat messages. Please slow down.',
});

export const faqFeedbackRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 feedback submissions per hour
  message: 'Too many feedback submissions. Please wait before submitting more feedback.',
});

/**
 * Input sanitization middleware
 */
export const sanitizeInput = (fields: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      for (const field of fields) {
        if (req.body[field]) {
          const sanitized = InputSanitizer.sanitizeText(req.body[field], {
            maxLength: 2000,
            trimWhitespace: true,
            allowHtml: false,
          });
          req.body[field] = sanitized;
        }
      }
      next();
    } catch (error) {
      logger.error(`Input sanitization error: ${error}`);
      res.status(400).json({
        error: 'Invalid input',
        message: 'Request contains invalid characters',
      });
    }
  };
};

/**
 * Support message validation middleware
 */
export const validateSupportMessage = (req: Request, res: Response, next: NextFunction) => {
  try {
    const { subject, category, message } = req.body;

    const validation = InputSanitizer.sanitizeSupportMessage({
      subject,
      category,
      message,
    });

    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid input data',
        details: validation.errors,
      });
    }

    // Replace request body with sanitized data
    req.body = {
      ...req.body,
      ...validation.sanitized,
    };

    next();
  } catch (error) {
    logger.error(`Support message validation error: ${error}`);
    res.status(400).json({
      error: 'Validation error',
      message: 'Failed to validate support message',
    });
  }
};

/**
 * Chat message validation middleware
 */
export const validateChatMessage = (req: Request, res: Response, next: NextFunction) => {
  try {
    const { message_text, message_type } = req.body;

    const validation = InputSanitizer.sanitizeChatMessage({
      messageText: message_text,
      messageType: message_type,
    });

    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid message data',
        details: validation.errors,
      });
    }

    // Replace request body with sanitized data
    req.body = {
      ...req.body,
      message_text: validation.sanitized.messageText,
      message_type: validation.sanitized.messageType,
    };

    next();
  } catch (error) {
    logger.error(`Chat message validation error: ${error}`);
    res.status(400).json({
      error: 'Validation error',
      message: 'Failed to validate chat message',
    });
  }
};

/**
 * IP-based blocking middleware
 */
export const checkBlockedIPs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    // Check if IP is in blocked list (this would be stored in database)
    const { data: blockedIP, error } = await supabase
      .from('blocked_ips')
      .select('id')
      .eq('ip_address', clientIP)
      .eq('is_active', true)
      .single();

    if (blockedIP && !error) {
      logger.warn(`Blocked IP attempted access: ${clientIP}`);
      return res.status(403).json({
        error: 'Access denied',
        message: 'Your IP address has been blocked',
      });
    }

    next();
  } catch (error) {
    // Continue if there's an error checking blocked IPs
    logger.error(`Error checking blocked IPs: ${error}`);
    next();
  }
};

/**
 * User-based blocking middleware
 */
export const checkBlockedUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return next();
    }

    // Check if user is blocked from support features
    const { data: blockedUser, error } = await supabase
      .from('user_restrictions')
      .select('restriction_type, expires_at')
      .eq('user_id', userId)
      .eq('is_active', true)
      .in('restriction_type', ['support_banned', 'chat_banned']);

    if (blockedUser && blockedUser.length > 0) {
      const activeRestrictions = blockedUser.filter(restriction => {
        if (!restriction.expires_at) return true;
        return new Date(restriction.expires_at) > new Date();
      });

      if (activeRestrictions.length > 0) {
        logger.warn(`Blocked user attempted support access: ${userId}`);
        return res.status(403).json({
          error: 'Access restricted',
          message: 'Your account has been restricted from using support features',
        });
      }
    }

    next();
  } catch (error) {
    // Continue if there's an error checking user restrictions
    logger.error(`Error checking user restrictions: ${error}`);
    next();
  }
};

/**
 * Request logging middleware for security monitoring
 */
export const securityLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Log request details
  logger.info(`Support request: ${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString(),
  });

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logLevel = res.statusCode >= 400 ? 'warn' : 'info';
    
    logger[logLevel](`Support response: ${res.statusCode}`, {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userId: req.user?.id,
    });
  });

  next();
};

/**
 * Suspicious activity detection
 */
export const detectSuspiciousActivity = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    const clientIP = req.ip;
    
    if (!userId) {
      return next();
    }

    // Check for rapid successive requests
    const recentRequests = await supabase
      .from('request_logs')
      .select('created_at')
      .eq('user_id', userId)
      .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
      .order('created_at', { ascending: false });

    if (recentRequests.data && recentRequests.data.length > 20) {
      logger.warn(`Suspicious activity detected: User ${userId} made ${recentRequests.data.length} requests in the last minute`);
      
      // Could implement automatic temporary blocking here
      return res.status(429).json({
        error: 'Suspicious activity detected',
        message: 'Please slow down your requests',
      });
    }

    // Log this request
    await supabase
      .from('request_logs')
      .insert({
        user_id: userId,
        ip_address: clientIP,
        endpoint: req.path,
        method: req.method,
        user_agent: req.get('User-Agent'),
        created_at: new Date().toISOString(),
      });

    next();
  } catch (error) {
    // Continue if there's an error with activity detection
    logger.error(`Error in suspicious activity detection: ${error}`);
    next();
  }
};

/**
 * Combined security middleware stack
 */
export const applySupportSecurity = [
  contentSecurityPolicy,
  securityLogger,
  checkBlockedIPs,
  checkBlockedUsers,
  detectSuspiciousActivity,
];
