import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/splash_screen.dart';
import '../../providers/auth_provider.dart';
import '../../screens/main_navigation.dart';
import '../../features/tournaments_list_screen.dart';
import '../../features/tournament_details_screen.dart';
import '../../features/create_tournament_screen.dart';
import '../../features/create_match_screen.dart';
import '../../screens/match_details_screen.dart';
import '../../screens/join_match_screen.dart';

import '../../screens/spectator_view_screen.dart';
import '../../screens/match_history_screen.dart';
import '../../screens/tournament_registration_screen_new.dart';
import '../../screens/tournament_result_submission_screen.dart';
import '../../screens/tournament_verification_status_screen.dart';
import '../../screens/tournament_match_details_screen.dart';
import '../../screens/add_money_screen.dart';
import '../../screens/start_match_wrapper.dart';
import '../../screens/result_submission_screen.dart';
import '../../screens/verification_status_screen.dart';
import '../../screens/admin_verification_screen.dart';
import '../../screens/settings_screen.dart';
import '../../screens/payment_settings_screen.dart';
import '../../screens/help_support_screen.dart';
import '../../screens/chat_screen.dart';

class AppRouter {
  // Authentication state notifier for redirection
  static final ValueNotifier<bool> isLoggedIn = ValueNotifier<bool>(false);

  // Create GoRouter configuration
  static GoRouter createRouter(BuildContext context) {
    // Check for authentication changes and update notifier
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    isLoggedIn.value = authProvider.isAuthenticated;

    return GoRouter(
      initialLocation: '/splash',
      debugLogDiagnostics: true,
      refreshListenable: authProvider,
      redirect: (context, state) {
        // Update logged in state whenever redirect is called
        isLoggedIn.value = authProvider.isAuthenticated;

        // Get current path
        final isSplashRoute = state.uri.path == '/splash';
        final isLoginRoute = state.uri.path == '/login';
        final isRegisterRoute = state.uri.path == '/register';
        final isAuthRoute = isLoginRoute || isRegisterRoute || isSplashRoute;

        // Don't redirect during loading state or on splash screen
        if (authProvider.isLoading || isSplashRoute) {
          return null;
        }

        // Logic for authentication redirects
        if (!authProvider.isAuthenticated && !isAuthRoute) {
          // Redirect to login if not authenticated and trying to access protected route
          return '/login';
        } else if (authProvider.isAuthenticated && isAuthRoute) {
          // Redirect to home if authenticated and trying to access auth routes
          return '/';
        }

        // No redirect needed
        return null;
      },
      routes: [
        // Splash Screen (initial route)
        GoRoute(
          path: '/splash',
          builder: (context, state) => const SplashScreen(),
        ),

        // Home route (protected)
        GoRoute(path: '/', builder: (context, state) => const MainNavigation()),

        // Login route
        GoRoute(
          path: '/login',
          builder: (context, state) => const LoginScreen(),
        ),

        // Register route
        GoRoute(
          path: '/register',
          builder: (context, state) => const RegisterScreen(),
        ),

        // Add more routes as needed
        // Tournaments List route (protected)
        GoRoute(
          path: '/tournaments',
          builder: (context, state) => const TournamentsListScreen(),
        ),
        // Create Tournament route (protected) - MUST come before parameterized route
        GoRoute(
          path: '/tournaments/create',
          builder: (context, state) => const CreateTournamentScreen(),
        ),
        // Tournament Details route (protected) - parameterized route comes last
        GoRoute(
          path: '/tournaments/:tournamentId',
          builder: (context, state) {
            final tournamentId = state.pathParameters['tournamentId'];
            return TournamentDetailsScreen(tournamentId: tournamentId!);
          },
        ),
        // Tournament Registration route (protected)
        GoRoute(
          path: '/tournaments/:tournamentId/register',
          builder: (context, state) {
            final tournamentId = state.pathParameters['tournamentId']!;
            return TournamentRegistrationScreen(tournamentId: tournamentId);
          },
        ),
        // Tournament Result Submission route (protected)
        GoRoute(
          path: '/tournaments/:tournamentId/submit-result',
          builder: (context, state) {
            final tournamentId = state.pathParameters['tournamentId']!;
            return TournamentResultSubmissionScreen(tournamentId: tournamentId);
          },
        ),
        // Tournament Verification Status route (protected)
        GoRoute(
          path: '/tournaments/:tournamentId/verification-status',
          builder: (context, state) {
            final tournamentId = state.pathParameters['tournamentId']!;
            return TournamentVerificationStatusScreen(
              tournamentId: tournamentId,
            );
          },
        ),
        // Tournament Match Details route (protected)
        GoRoute(
          path: '/tournaments/:tournamentId/match-details',
          builder: (context, state) {
            final tournamentId = state.pathParameters['tournamentId']!;
            return TournamentMatchDetailsScreen(tournamentId: tournamentId);
          },
        ),
        // Create Match route (protected)
        GoRoute(
          path: '/matches/create',
          builder: (context, state) => const CreateMatchScreen(),
        ),
        // Match Details route
        GoRoute(
          path: '/matches/:matchId',
          builder: (context, state) {
            final matchId = state.pathParameters['matchId']!;
            return MatchDetailsScreen(matchId: matchId);
          },
        ),
        // Join Match route
        GoRoute(
          path: '/matches/:matchId/join',
          builder: (context, state) {
            final matchId = state.pathParameters['matchId']!;
            return JoinMatchScreen(matchId: matchId);
          },
        ),

        // Spectator View route
        GoRoute(
          path: '/matches/:matchId/spectate',
          builder: (context, state) {
            final id = state.pathParameters['matchId']!;
            return SpectatorViewScreen(matchId: id);
          },
        ),
        // Match History route
        GoRoute(
          path: '/match-history',
          builder: (context, state) => const MatchHistoryScreen(),
        ),
        // Add Money / Wallet Top-up route
        GoRoute(
          path: '/wallet/add-money',
          builder: (context, state) => const AddMoneyScreen(),
        ),
        // Start Match route
        GoRoute(
          path: '/matches/:matchId/start',
          builder: (context, state) {
            final matchId = state.pathParameters['matchId']!;
            return StartMatchWrapper(matchId: matchId);
          },
        ),
        // Result Submission route
        GoRoute(
          path: '/matches/:matchId/submit-result',
          builder: (context, state) {
            final matchId = state.pathParameters['matchId']!;
            return ResultSubmissionScreen(matchId: matchId);
          },
        ),
        // Redirect from old incorrect route to correct one
        GoRoute(
          path: '/matches/:matchId/submit-results',
          redirect: (context, state) {
            final matchId = state.pathParameters['matchId']!;
            return '/matches/$matchId/submit-result';
          },
        ),
        // Verification Status route
        GoRoute(
          path: '/matches/:matchId/verification-status',
          builder: (context, state) {
            final matchId = state.pathParameters['matchId']!;
            return VerificationStatusScreen(matchId: matchId);
          },
        ),
        // Admin Verification route
        GoRoute(
          path: '/admin/verification',
          builder: (context, state) => const AdminVerificationScreen(),
        ),
        // Settings route
        GoRoute(
          path: '/settings',
          builder: (context, state) => const SettingsScreen(),
        ),
        // Payment Settings route
        GoRoute(
          path: '/settings/payment',
          builder: (context, state) => const PaymentSettingsScreen(),
        ),
        // Help & Support route
        GoRoute(
          path: '/help-support',
          builder: (context, state) => const HelpSupportScreen(),
        ),
        // Chat route
        GoRoute(path: '/chat', builder: (context, state) => const ChatScreen()),
        // Chat with session ID route
        GoRoute(
          path: '/chat/:sessionId',
          builder: (context, state) {
            final sessionId = state.pathParameters['sessionId'];
            return ChatScreen(sessionId: sessionId);
          },
        ),
      ],
      errorBuilder:
          (context, state) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    '404 - Page not found',
                    style: TextStyle(fontSize: 24),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.go('/'),
                    child: const Text('Go Home'),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
