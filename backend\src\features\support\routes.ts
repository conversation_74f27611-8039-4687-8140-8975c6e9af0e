/**
 * Support System Routes
 * Handles help & support related API endpoints including contact forms, chat, and FAQ
 */

import express, { RequestHandler } from 'express';
import * as supportController from './controllers/supportController';
import * as chatController from './controllers/chatController';
import * as faqController from './controllers/faqController';
import { authenticate, authorize } from '../../middleware/auth';
import { validate } from '../../middleware/validate';
import { supportRateLimit } from '../../middleware/supportRateLimit';
import {
  applySupportSecurity,
  validateSupportMessage,
  validateChatMessage,
  supportMessageRateLimit,
  chatMessageRateLimit,
  faqFeedbackRateLimit
} from '../../middleware/securityMiddleware';
import {
  createSupportMessageSchema,
  supportMessageReplySchema,
  createChatSessionSchema,
  sendChatMessageSchema,
  updateSupportMessageSchema,
  faqFeedbackSchema,
  supportMessageFiltersSchema,
  chatSessionFiltersSchema
} from './validations';

// Helper function to ensure middleware type compatibility
const wrapWithValidate = (schema: any, source = 'body'): RequestHandler => {
  return validate(schema, source as any);
};

const router = express.Router();

// Public FAQ routes (no authentication required)
router.get('/faq/categories', faqController.getFaqCategories);
router.get('/faq/items', faqController.getFaqItems);
router.get('/faq/categories/:categoryId/items', faqController.getFaqItemsByCategory);

// Protected routes requiring authentication
router.use(authenticate);

// Support Messages Routes
router.post('/messages',
  supportRateLimit, // Rate limiting for support message creation
  wrapWithValidate(createSupportMessageSchema),
  supportController.createSupportMessage
);

router.get('/messages',
  wrapWithValidate(supportMessageFiltersSchema, 'query'),
  supportController.getUserSupportMessages
);

router.get('/messages/:messageId',
  supportController.getSupportMessageById
);

router.get('/messages/:messageId/replies',
  supportController.getSupportMessageReplies
);

// Chat System Routes
router.post('/chat/sessions',
  wrapWithValidate(createChatSessionSchema),
  chatController.createChatSession
);

router.get('/chat/sessions',
  wrapWithValidate(chatSessionFiltersSchema, 'query'),
  chatController.getUserChatSessions
);

router.get('/chat/sessions/:sessionId',
  chatController.getChatSessionById
);

router.post('/chat/sessions/:sessionId/messages',
  wrapWithValidate(sendChatMessageSchema),
  chatController.sendChatMessage
);

router.get('/chat/sessions/:sessionId/messages',
  chatController.getChatMessages
);

router.put('/chat/sessions/:sessionId/close',
  chatController.closeChatSession
);

// FAQ Feedback Routes
router.post('/faq/items/:itemId/feedback',
  wrapWithValidate(faqFeedbackSchema),
  faqController.submitFaqFeedback
);

router.get('/faq/items/:itemId',
  faqController.getFaqItemById
);

// Admin-only routes
router.use(authorize(['admin', 'manager', 'spectator']));

// Admin Support Message Management
router.get('/admin/messages',
  wrapWithValidate(supportMessageFiltersSchema, 'query'),
  supportController.getAllSupportMessages
);

router.put('/admin/messages/:messageId',
  wrapWithValidate(updateSupportMessageSchema),
  supportController.updateSupportMessage
);

router.post('/admin/messages/:messageId/replies',
  wrapWithValidate(supportMessageReplySchema),
  supportController.createSupportMessageReply
);

router.put('/admin/messages/:messageId/assign',
  supportController.assignSupportMessage
);

router.put('/admin/messages/:messageId/status',
  supportController.updateSupportMessageStatus
);

// Admin Chat Management
router.get('/admin/chat/sessions',
  wrapWithValidate(chatSessionFiltersSchema, 'query'),
  chatController.getAllChatSessions
);

router.put('/admin/chat/sessions/:sessionId/assign',
  chatController.assignChatSession
);

router.post('/admin/chat/sessions/:sessionId/messages',
  wrapWithValidate(sendChatMessageSchema),
  chatController.sendAdminChatMessage
);

router.put('/admin/chat/sessions/:sessionId/transfer',
  chatController.transferChatSession
);

// Admin FAQ Management
router.post('/admin/faq/categories',
  authorize(['admin', 'manager']),
  faqController.createFaqCategory
);

router.put('/admin/faq/categories/:categoryId',
  authorize(['admin', 'manager']),
  faqController.updateFaqCategory
);

router.delete('/admin/faq/categories/:categoryId',
  authorize(['admin', 'manager']),
  faqController.deleteFaqCategory
);

router.post('/admin/faq/items',
  authorize(['admin', 'manager']),
  faqController.createFaqItem
);

router.put('/admin/faq/items/:itemId',
  authorize(['admin', 'manager']),
  faqController.updateFaqItem
);

router.delete('/admin/faq/items/:itemId',
  authorize(['admin', 'manager']),
  faqController.deleteFaqItem
);

router.get('/admin/faq/analytics',
  faqController.getFaqAnalytics
);

// Support Analytics Routes
router.get('/admin/analytics/overview',
  supportController.getSupportAnalytics
);

router.get('/admin/analytics/response-times',
  supportController.getResponseTimeAnalytics
);

router.get('/admin/analytics/satisfaction',
  supportController.getSatisfactionAnalytics
);

export default router;
