"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabaseClient"
import { 
  MessageSquare, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  TrendingUp,
  Users,
  MessageCircle,
  HelpCircle
} from "lucide-react"
import Link from "next/link"

interface SupportStats {
  totalMessages: number
  newMessages: number
  inProgressMessages: number
  resolvedMessages: number
  averageResponseTime: string
  totalChatSessions: number
  activeChatSessions: number
  totalFaqViews: number
}

interface RecentMessage {
  id: string
  subject: string
  category: string
  status: string
  priority: string
  created_at: string
  user: {
    name: string
    email: string
  }
}

export default function SupportOverviewPage() {
  const [stats, setStats] = useState<SupportStats | null>(null)
  const [recentMessages, setRecentMessages] = useState<RecentMessage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchSupportData()
  }, [])

  const fetchSupportData = async () => {
    try {
      setIsLoading(true)
      const supabase = createClient()

      // Fetch support message stats
      const { data: messages, error: messagesError } = await supabase
        .from('support_messages')
        .select('status, priority, created_at')

      if (messagesError) throw messagesError

      // Fetch recent messages
      const { data: recent, error: recentError } = await supabase
        .from('support_messages')
        .select(`
          id,
          subject,
          category,
          status,
          priority,
          created_at,
          user:users(name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(5)

      if (recentError) throw recentError

      // Fetch chat session stats
      const { data: chatSessions, error: chatError } = await supabase
        .from('chat_sessions')
        .select('status, created_at')

      if (chatError) throw chatError

      // Calculate stats
      const totalMessages = messages?.length || 0
      const newMessages = messages?.filter(m => m.status === 'new').length || 0
      const inProgressMessages = messages?.filter(m => m.status === 'in_progress').length || 0
      const resolvedMessages = messages?.filter(m => m.status === 'resolved').length || 0
      
      const totalChatSessions = chatSessions?.length || 0
      const activeChatSessions = chatSessions?.filter(s => s.status === 'active').length || 0

      setStats({
        totalMessages,
        newMessages,
        inProgressMessages,
        resolvedMessages,
        averageResponseTime: "2.5 hours", // This would be calculated from actual data
        totalChatSessions,
        activeChatSessions,
        totalFaqViews: 1250 // This would come from FAQ analytics
      })

      setRecentMessages(recent || [])
    } catch (error) {
      console.error('Error fetching support data:', error)
      toast({
        title: "Error",
        description: "Failed to load support data",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'normal': return 'bg-blue-100 text-blue-800'
      case 'low': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-48" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {[...Array(2)].map((_, i) => (
            <Skeleton key={i} className="h-64" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Support Overview</h1>
          <p className="text-muted-foreground">Monitor and manage customer support activities</p>
        </div>
        <Button onClick={fetchSupportData}>
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalMessages || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.newMessages || 0} new messages
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.inProgressMessages || 0}</div>
            <p className="text-xs text-muted-foreground">
              Being handled by admins
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.resolvedMessages || 0}</div>
            <p className="text-xs text-muted-foreground">
              +12% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.averageResponseTime || "N/A"}</div>
            <p className="text-xs text-muted-foreground">
              -15% from last week
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Chat Stats */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Chat Sessions
            </CardTitle>
            <CardDescription>Real-time chat activity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Sessions</span>
                <span className="text-2xl font-bold">{stats?.totalChatSessions || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Active Now</span>
                <span className="text-lg font-semibold text-green-600">{stats?.activeChatSessions || 0}</span>
              </div>
              <Link href="/dashboard/support/chat">
                <Button variant="outline" className="w-full">
                  Manage Chat Sessions
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              FAQ Analytics
            </CardTitle>
            <CardDescription>Self-service help content</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Views</span>
                <span className="text-2xl font-bold">{stats?.totalFaqViews || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">This Week</span>
                <span className="text-lg font-semibold text-blue-600">+23%</span>
              </div>
              <Link href="/dashboard/support/faq">
                <Button variant="outline" className="w-full">
                  Manage FAQ
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Messages */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Support Messages</CardTitle>
          <CardDescription>Latest customer inquiries and requests</CardDescription>
        </CardHeader>
        <CardContent>
          {recentMessages.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No recent messages</h3>
              <p className="text-muted-foreground">New support messages will appear here.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {recentMessages.map((message) => (
                <div key={message.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{message.subject}</h4>
                      <Badge className={getStatusColor(message.status)}>
                        {message.status.replace('_', ' ')}
                      </Badge>
                      <Badge className={getPriorityColor(message.priority)}>
                        {message.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      From {message.user.name} • {new Date(message.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <Link href={`/dashboard/support/messages`}>
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </Link>
                </div>
              ))}
              <div className="pt-4 border-t">
                <Link href="/dashboard/support/messages">
                  <Button variant="outline" className="w-full">
                    View All Messages
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Link href="/dashboard/support/messages">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-center">
                <MessageSquare className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold">Manage Messages</h3>
                <p className="text-sm text-muted-foreground">View and respond to support requests</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/dashboard/support/chat">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-center">
                <MessageCircle className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold">Live Chat</h3>
                <p className="text-sm text-muted-foreground">Handle real-time chat sessions</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/dashboard/support/faq">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-center">
                <HelpCircle className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold">FAQ Management</h3>
                <p className="text-sm text-muted-foreground">Update help documentation</p>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
