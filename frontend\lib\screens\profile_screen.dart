import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:wiggyz_app/theme_provider.dart';
import 'package:wiggyz_app/providers/user_provider.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/services/user_service.dart';
import 'package:wiggyz_app/services/user_stats_service.dart';
import 'package:wiggyz_app/services/activities_service.dart';
import 'package:wiggyz_app/models/user_stats.dart';
import 'package:wiggyz_app/models/user_activity.dart';
import 'package:wiggyz_app/screens/profile_edit_screen.dart';
import 'package:wiggyz_app/screens/achievement_details_screen.dart';
import 'package:wiggyz_app/screens/activity_history_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wiggyz_app/shared/widgets/widgets.dart';
import 'package:wiggyz_app/services/image_compression_service.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final ImagePicker _picker = ImagePicker();
  late UserService _userService;
  late UserStatsService _userStatsService;
  late ActivitiesService _activitiesService;

  bool _isLoading = true;
  bool _isRefreshing = false;
  bool _isUploadingImage = false;
  List<Map<String, dynamic>>? _achievements;
  List<UserActivity>? _activities;
  UserStats? _userStats;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeUserService();
    });
  }

  @override
  void dispose() {
    // Cancel any ongoing async operations by setting a flag
    // Note: We don't have explicit timers or streams to cancel in this widget,
    // but we should ensure setState is not called after dispose
    super.dispose();
  }

  Future<void> _initializeUserService() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    debugPrint('Profile Screen - Auth status: ${authProvider.isAuthenticated}');
    debugPrint('Profile Screen - User data: ${authProvider.userData}');
    debugPrint('Profile Screen - Current username: ${userProvider.username}');
    debugPrint('Profile Screen - Current email: ${userProvider.email}');

    _userService = UserService(userProvider, authProvider);
    _userStatsService = UserStatsService(authProvider);
    _activitiesService = ActivitiesService(authProvider);

    // Fetch profile and related data
    await _loadProfileData();
  }

  Future<void> _loadProfileData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // First, sync user data from auth provider
      await _userService.syncUserDataFromAuth();

      // Then fetch complete profile data from API
      await _userService.fetchUserProfile();
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    }

    // Load other data independently to prevent one failure from blocking everything
    await _loadAchievements();
    await _loadActivities();
    await _loadUserStatistics();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAchievements() async {
    try {
      _achievements = await _userService.getUserAchievements();
    } catch (e) {
      debugPrint('Error loading achievements: $e');
      _achievements = []; // Set empty list as fallback
    }
  }

  Future<void> _loadActivities() async {
    try {
      _activities = await _activitiesService.getRecentActivities(limit: 5);
    } catch (e) {
      debugPrint('Error loading activities: $e');
      _activities = []; // Set empty list as fallback
    }
  }

  Future<void> _loadUserStatistics() async {
    try {
      _userStats = await _userStatsService.getUserStatistics();
    } catch (e) {
      debugPrint('Error loading user statistics: $e');
      // _userStats remains null, which will trigger fallback UI
    }
  }

  Future<void> _retryLoadStatistics() async {
    await _loadUserStatistics();
    if (mounted) {
      setState(() {}); // Trigger rebuild to show updated statistics
    }
  }

  Future<void> _refreshProfile() async {
    if (_isRefreshing || !mounted) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      await _loadProfileData();
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar.text(
        title: 'Profile',
        style: GoogleFonts.poppins(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.onSurface,
        ),
        backgroundColor:
            Theme.of(context).appBarTheme.backgroundColor ??
            Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        showBackButton: false,
        actions: [
          IconButton(
            icon: Icon(
              Icons.edit,
              color:
                  Theme.of(context).appBarTheme.iconTheme?.color ??
                  Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            tooltip: 'Edit Profile',
            onPressed: () async {
              // Navigate to profile edit screen
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ProfileEditScreen(),
                ),
              );

              // If returned with success (true), refresh profile data
              if (result == true) {
                _refreshProfile();
              }
            },
          ),
          IconButton(
            icon: Icon(
              Icons.settings_outlined,
              color:
                  Theme.of(context).appBarTheme.iconTheme?.color ??
                  Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            onPressed: () {
              // Navigate to settings
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).primaryColor,
                ),
              )
              : RefreshIndicator(
                onRefresh: _refreshProfile,
                color: Theme.of(context).primaryColor,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildProfileHeader(),
                      const SizedBox(height: 20),
                      _buildStatsSection(),
                      const SizedBox(height: 20),
                      _buildAchievementsSection(),
                      const SizedBox(height: 20),
                      _buildRecentWinnersSection(),
                      const SizedBox(height: 20),
                      _buildActionButtons(themeProvider),
                    ],
                  ),
                ),
              ),
    );
  }

  // Method to show image source selection dialog
  void _showImageSourceOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Change Profile Picture',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(
                    context,
                  ).primaryColor.withOpacity(0.1),
                  child: Icon(
                    Icons.photo_camera,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                title: Text('Take Photo', style: GoogleFonts.poppins()),
                onTap: () {
                  Navigator.of(context).pop();
                  _getImage(ImageSource.camera);
                },
              ),
              const SizedBox(height: 10),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(
                    context,
                  ).primaryColor.withOpacity(0.1),
                  child: Icon(
                    Icons.photo_library,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                title: Text(
                  'Choose from Gallery',
                  style: GoogleFonts.poppins(),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _getImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Method to get image from camera or gallery
  Future<void> _getImage(ImageSource source) async {
    try {
      final pickedFile = await _picker.pickImage(source: source);
      if (pickedFile == null || !mounted) return;

      setState(() {
        _isUploadingImage = true;
      });

      // Debug logging
      debugPrint('=== PROFILE IMAGE UPLOAD DEBUG ===');
      debugPrint('Selected file path: ${pickedFile.path}');
      debugPrint('Selected file name: ${pickedFile.name}');
      debugPrint('Selected file mimeType: ${pickedFile.mimeType}');

      // Check file size before processing (web-compatible)
      try {
        final imageBytes = await pickedFile.readAsBytes();
        debugPrint(
          'Original file size: ${imageBytes.length / (1024 * 1024)} MB',
        );
      } catch (e) {
        debugPrint('Error getting file size: $e');
      }

      // Show progress indicator
      _showProgressSnackBar('Processing image...');

      // Compress and validate image using the XFile-compatible method
      debugPrint('Calling processImageFromXFile...');
      final compressedFile =
          await ImageCompressionService.processImageFromXFile(
            pickedFile,
            pickedFile.name,
            pickedFile.mimeType,
          );

      debugPrint(
        'processImageFromXFile result: ${compressedFile != null ? 'SUCCESS' : 'FAILED'}',
      );

      if (compressedFile == null) {
        debugPrint('Image processing failed - showing error to user');
        _showErrorSnackBar(
          'Failed to process image. Please try a smaller image.',
        );
        if (mounted) {
          setState(() {
            _isUploadingImage = false;
          });
        }
        return;
      }

      // Show upload progress
      _showProgressSnackBar('Uploading image...');

      // Upload the compressed image using UserService
      // Use XFile-compatible method for web compatibility
      final success =
          kIsWeb
              ? await _userService.uploadProfileImageFromXFile(pickedFile)
              : await _userService.uploadProfileImage(compressedFile);

      if (mounted) {
        setState(() {
          _isUploadingImage = false;
        });
      }

      if (success) {
        _showSuccessSnackBar('Profile image updated successfully!');
        // Refresh profile data to show new image
        await _loadProfileData();
      } else {
        _showErrorSnackBar('Failed to upload profile image. Please try again.');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isUploadingImage = false;
        });
      }
      _showErrorSnackBar('Error uploading image: $e');
    }
  }

  // Helper methods for showing different types of snack bars
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showProgressSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          Stack(
            children: [
              // Avatar container
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).primaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Base layer - initials as fallback
                    Consumer<UserProvider>(
                      builder: (context, userProvider, _) {
                        final username =
                            userProvider.profile?.username ??
                            userProvider.username;
                        final initials =
                            username.isNotEmpty
                                ? username
                                    .split(' ')
                                    .map(
                                      (e) =>
                                          e.isNotEmpty
                                              ? e[0].toUpperCase()
                                              : '',
                                    )
                                    .take(2)
                                    .join('')
                                : 'WZ';

                        return Center(
                          child: Text(
                            initials,
                            style: GoogleFonts.poppins(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onPrimary,
                            ),
                          ),
                        );
                      },
                    ),
                    // Top layer - profile image (local file or network)
                    Consumer<UserProvider>(
                      builder: (context, userProvider, _) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(40),
                          child:
                              userProvider.profileImageFile != null
                                  // Show local image if available
                                  ? Image.file(
                                    userProvider.profileImageFile!,
                                    fit: BoxFit.cover,
                                    width: 80,
                                    height: 80,
                                  )
                                  // Otherwise show network image
                                  : (userProvider.profileImageUrl.isNotEmpty
                                      ? CachedNetworkImage(
                                        imageUrl: userProvider.profileImageUrl,
                                        fit: BoxFit.cover,
                                        placeholder:
                                            (context, url) => Center(
                                              child: CircularProgressIndicator(
                                                color: Colors.white,
                                                strokeWidth: 2,
                                              ),
                                            ),
                                        errorWidget:
                                            (context, url, error) =>
                                                const SizedBox(), // Show nothing on error (falls back to initials)
                                      )
                                      : const SizedBox()), // Empty box if no URL (falls back to initials)
                        );
                      },
                    ),
                  ],
                ),
              ),
              // Camera icon for changing profile picture
              Positioned(
                right: 0,
                bottom: 0,
                child: InkWell(
                  onTap: _showImageSourceOptions,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Consumer<UserProvider>(
                  builder: (context, userProvider, _) {
                    final username =
                        userProvider.profile?.username ?? userProvider.username;
                    final email =
                        userProvider.profile?.email ?? userProvider.email;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          username.isNotEmpty ? username : 'Username',
                          style: GoogleFonts.poppins(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          email.isNotEmpty ? email : 'Email not available',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Theme.of(
                              context,
                            ).textTheme.bodySmall?.color?.withOpacity(0.7),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 10),
                Consumer<UserProvider>(
                  builder: (context, userProvider, _) {
                    final role = userProvider.profile?.role ?? 'Player';

                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        role,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Stats',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              if (_userStats == null)
                IconButton(
                  icon: Icon(
                    Icons.refresh,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  onPressed: _retryLoadStatistics,
                  tooltip: 'Retry loading statistics',
                ),
            ],
          ),
          const SizedBox(height: 15),
          _userStats != null
              ? Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildStatItem('Games', _userStats!.gamesPlayed.toString()),
                  _buildStatItem(
                    'Tournaments',
                    _userStats!.tournamentsJoined.toString(),
                  ),
                  _buildStatItem('Wins', _userStats!.wins.toString()),
                ],
              )
              : Consumer<UserProvider>(
                builder: (context, userProvider, _) {
                  // Fallback to profile stats or show error state
                  final gamesPlayed =
                      userProvider.profile?.stats?['gamesPlayed']?.toString();
                  final tournamentsJoined =
                      userProvider.profile?.stats?['tournamentsJoined']
                          ?.toString();
                  final wins = userProvider.profile?.stats?['wins']?.toString();

                  // If we have fallback data, show it
                  if (gamesPlayed != null ||
                      tournamentsJoined != null ||
                      wins != null) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildStatItem('Games', gamesPlayed ?? '0'),
                        _buildStatItem('Tournaments', tournamentsJoined ?? '0'),
                        _buildStatItem('Wins', wins ?? '0'),
                      ],
                    );
                  }

                  // Show error state when no data is available
                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.errorContainer.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).colorScheme.error.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Theme.of(context).colorScheme.error,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Unable to load statistics. Please check your connection and try again.',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Theme.of(context).colorScheme.error,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.28,
      padding: const EdgeInsets.symmetric(vertical: 15),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 5),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentWinnersSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ActivityHistoryScreen(),
                    ),
                  );
                },
                child: Text(
                  'View All',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          if (_activities == null || _activities!.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 30),
              alignment: Alignment.center,
              child: Text(
                'No recent activities found',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            )
          else
            ..._activities!.take(3).map((activity) {
              return Column(
                children: [
                  _buildWinningItem(
                    tournament: activity.title,
                    amount: activity.formattedAmount,
                    time: activity.timeAgo,
                  ),
                  const SizedBox(height: 10),
                ],
              );
            }),
        ],
      ),
    );
  }

  Widget _buildWinningItem({
    required String tournament,
    required String amount,
    required String time,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              FontAwesomeIcons.trophy,
              color: Theme.of(context).primaryColor,
              size: 18,
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tournament,
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                Text(
                  time,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildActionButton(
            icon: Icons.person_outline,
            label: 'Personal Information',
            onTap: () async {
              // Navigate to profile edit screen
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ProfileEditScreen(),
                ),
              );

              // If returned with success (true), refresh profile data
              if (result == true) {
                _refreshProfile();
              }
            },
          ),
          _buildActionButton(
            icon: Icons.settings_outlined,
            label: 'Settings',
            onTap: () {
              if (mounted) {
                context.go('/settings');
              }
            },
          ),
          _buildActionButton(
            icon: Icons.history,
            label: 'Match History',
            onTap: () {
              if (mounted) {
                context.go('/match-history');
              }
            },
          ),
          _buildActionButton(icon: Icons.help_outline, label: 'Help & Support'),
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: InkWell(
              onTap: () {
                themeProvider.toggleTheme(!themeProvider.isDarkMode);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 15,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        themeProvider.isDarkMode
                            ? Icons.dark_mode_outlined
                            : Icons.light_mode_outlined,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                      child: Text(
                        'Dark Mode',
                        style: GoogleFonts.poppins(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    Switch(
                      value: themeProvider.isDarkMode,
                      onChanged: (value) {
                        themeProvider.toggleTheme(value);
                      },
                      activeColor: Theme.of(context).primaryColor,
                    ),
                  ],
                ),
              ),
            ),
          ),
          _buildActionButton(
            icon: Icons.logout,
            label: 'Logout',
            isDestructive: true,
            onTap: () async {
              // Show confirmation dialog
              final shouldLogout =
                  await showDialog<bool>(
                    context: context,
                    builder:
                        (context) => AlertDialog(
                          title: Text('Confirm Logout'),
                          content: Text('Are you sure you want to log out?'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(false),
                              child: Text('Cancel'),
                            ),
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(true),
                              child: Text('Logout'),
                            ),
                          ],
                        ),
                  ) ??
                  false;

              if (shouldLogout) {
                // Show loading indicator
                setState(() {
                  _isLoading = true;
                });

                try {
                  // Get the auth provider
                  final authProvider = Provider.of<AuthProvider>(
                    context,
                    listen: false,
                  );
                  // Log out
                  await authProvider.logout();

                  // Clear user data on logout is handled by UserService through the auth provider
                } catch (e) {
                  debugPrint('Error logging out: $e');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error logging out: $e')),
                  );
                } finally {
                  if (mounted) {
                    setState(() {
                      _isLoading = false;
                    });
                  }
                }
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    bool isDestructive = false,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 15),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface.withOpacity(0.7),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color:
                      isDestructive
                          ? Colors.red.withOpacity(0.1)
                          : Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color:
                      isDestructive
                          ? Colors.red
                          : Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color:
                        isDestructive
                            ? Colors.red
                            : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).textTheme.bodySmall?.color,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Bottom navigation is now handled by MainNavigation

  Widget _buildAchievementsSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Achievements',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              Text(
                'View All',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: CircularProgressIndicator(),
              ),
            )
          else if (_achievements == null || _achievements!.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 30),
              alignment: Alignment.center,
              child: Text(
                'No achievements yet',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            )
          else
            SizedBox(
              height: 130,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount:
                    _achievements!.length > 5 ? 5 : _achievements!.length,
                itemBuilder: (context, index) {
                  final achievement = _achievements![index];
                  final title = achievement['title'] ?? 'Achievement';
                  final description =
                      achievement['description'] ??
                      'Complete a task to earn this achievement';
                  final iconData = _getAchievementIcon(
                    achievement['type'] ?? 'default',
                  );

                  return GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => AchievementDetailsScreen(
                                achievement: achievement,
                              ),
                        ),
                      );
                    },
                    child: Container(
                      width: 110,
                      margin: EdgeInsets.only(right: 10),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surface.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 45,
                            height: 45,
                            decoration: BoxDecoration(
                              color: Theme.of(
                                context,
                              ).primaryColor.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              iconData,
                              color: Theme.of(context).primaryColor,
                              size: 24,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Text(
                              title,
                              textAlign: TextAlign.center,
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 6),
                            child: Text(
                              description,
                              textAlign: TextAlign.center,
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                color:
                                    Theme.of(
                                      context,
                                    ).textTheme.bodySmall?.color,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  IconData _getAchievementIcon(String type) {
    switch (type.toLowerCase()) {
      case 'win':
        return Icons.emoji_events;
      case 'play':
        return Icons.sports_esports;
      case 'social':
        return Icons.people;
      case 'purchase':
        return Icons.shopping_bag;
      case 'rank':
        return Icons.military_tech;
      case 'level':
        return Icons.trending_up;
      default:
        return Icons.star;
    }
  }
}
