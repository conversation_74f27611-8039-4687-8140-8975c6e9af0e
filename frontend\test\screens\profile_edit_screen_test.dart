import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:wiggyz_app/screens/profile_edit_screen.dart';
import 'package:wiggyz_app/providers/user_provider.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/models/profile.dart';

// Generate mocks
@GenerateMocks([UserProvider, AuthProvider])
import 'profile_edit_screen_test.mocks.dart';

void main() {
  group('ProfileEditScreen Tests', () {
    late MockUserProvider mockUserProvider;
    late MockAuthProvider mockAuthProvider;

    setUp(() {
      mockUserProvider = MockUserProvider();
      mockAuthProvider = MockAuthProvider();

      // Setup default mock behavior
      when(mockUserProvider.username).thenReturn('testuser');
      when(mockUserProvider.email).thenReturn('<EMAIL>');
      when(mockUserProvider.bio).thenReturn('Test bio');
      when(mockUserProvider.location).thenReturn('Test location');
      when(mockUserProvider.profileImageUrl).thenReturn('');
      when(mockUserProvider.profileImageFile).thenReturn(null);
      when(mockUserProvider.isLoading).thenReturn(false);
      when(mockUserProvider.profile).thenReturn(
        Profile(
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          phone: '+**********',
          bio: 'Test bio',
          location: 'Test location',
          freeFireProfile: GameProfile(
            username: 'ff_user',
            uid: '123456789',
            level: 50,
            server: 'Asia',
            rank: 'Diamond',
            preferredMode: 'Squad',
          ),
          pubgProfile: GameProfile(
            username: 'pubg_user',
            uid: '987654321',
            level: 45,
            server: 'Asia',
            rank: 'Crown',
            preferredMode: 'Duo',
          ),
        ),
      );

      when(mockAuthProvider.isAuthenticated).thenReturn(true);
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: MultiProvider(
          providers: [
            ChangeNotifierProvider<UserProvider>.value(value: mockUserProvider),
            ChangeNotifierProvider<AuthProvider>.value(value: mockAuthProvider),
          ],
          child: const ProfileEditScreen(),
        ),
      );
    }

    testWidgets('should display profile edit screen with tabs', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check if the screen title is displayed
      expect(find.text('Edit Profile'), findsOneWidget);

      // Check if tabs are displayed
      expect(find.text('Personal'), findsOneWidget);
      expect(find.text('Free Fire'), findsOneWidget);
      expect(find.text('PUBG'), findsOneWidget);

      // Check if save button is displayed
      expect(find.text('Save'), findsOneWidget);
    });

    testWidgets('should display personal information fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check if personal information fields are displayed
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Phone Number'), findsOneWidget);
      expect(find.text('Bio'), findsOneWidget);
      expect(find.text('Location'), findsOneWidget);
    });

    testWidgets('should switch to Free Fire tab and display game fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap on Free Fire tab
      await tester.tap(find.text('Free Fire'));
      await tester.pumpAndSettle();

      // Check if Free Fire fields are displayed
      expect(find.text('Free Fire Username'), findsOneWidget);
      expect(find.text('Player ID (UID)'), findsOneWidget);
      expect(find.text('Level'), findsOneWidget);
      expect(find.text('Server'), findsOneWidget);
      expect(find.text('Rank'), findsOneWidget);
      expect(find.text('Preferred Mode'), findsOneWidget);
    });

    testWidgets('should switch to PUBG tab and display game fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap on PUBG tab
      await tester.tap(find.text('PUBG'));
      await tester.pumpAndSettle();

      // Check if PUBG fields are displayed
      expect(find.text('PUBG Username'), findsOneWidget);
      expect(find.text('Player ID (UID)'), findsOneWidget);
      expect(find.text('Level'), findsOneWidget);
      expect(find.text('Server'), findsOneWidget);
      expect(find.text('Rank'), findsOneWidget);
      expect(find.text('Preferred Mode'), findsOneWidget);
    });

    testWidgets('should display profile picture section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check if profile picture section is displayed
      expect(find.text('Tap to change profile picture'), findsOneWidget);
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('should validate required fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Clear username field
      await tester.enterText(find.byType(TextFormField).first, '');
      
      // Tap save button
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Check if validation error is displayed
      expect(find.text('Username is required'), findsOneWidget);
    });
  });
}
