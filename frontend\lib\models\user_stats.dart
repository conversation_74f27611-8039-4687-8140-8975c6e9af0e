/// Model for user statistics data
class UserStats {
  final String userId;
  final int gamesPlayed;
  final int tournamentsJoined;
  final int wins;
  final double totalEarnings;
  final double winRate;
  final double averagePosition;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastCalculated;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserStats({
    required this.userId,
    required this.gamesPlayed,
    required this.tournamentsJoined,
    required this.wins,
    required this.totalEarnings,
    required this.winRate,
    required this.averagePosition,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastCalculated,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create UserStats from JSON
  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      userId: json['userId'] ?? '',
      gamesPlayed: json['gamesPlayed'] ?? 0,
      tournamentsJoined: json['tournamentsJoined'] ?? 0,
      wins: json['wins'] ?? 0,
      totalEarnings: (json['totalEarnings'] ?? 0).toDouble(),
      winRate: (json['winRate'] ?? 0).toDouble(),
      averagePosition: (json['averagePosition'] ?? 0).toDouble(),
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      lastCalculated: DateTime.parse(
        json['lastCalculated'] ?? DateTime.now().toIso8601String(),
      ),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// Convert UserStats to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'gamesPlayed': gamesPlayed,
      'tournamentsJoined': tournamentsJoined,
      'wins': wins,
      'totalEarnings': totalEarnings,
      'winRate': winRate,
      'averagePosition': averagePosition,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastCalculated': lastCalculated.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  UserStats copyWith({
    String? userId,
    int? gamesPlayed,
    int? tournamentsJoined,
    int? wins,
    double? totalEarnings,
    double? winRate,
    double? averagePosition,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastCalculated,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserStats(
      userId: userId ?? this.userId,
      gamesPlayed: gamesPlayed ?? this.gamesPlayed,
      tournamentsJoined: tournamentsJoined ?? this.tournamentsJoined,
      wins: wins ?? this.wins,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      winRate: winRate ?? this.winRate,
      averagePosition: averagePosition ?? this.averagePosition,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastCalculated: lastCalculated ?? this.lastCalculated,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get total games (matches + tournaments)
  int get totalGames => gamesPlayed + tournamentsJoined;

  /// Get formatted win rate as percentage string
  String get winRateFormatted => '${winRate.toStringAsFixed(1)}%';

  /// Get formatted average position
  String get averagePositionFormatted => averagePosition.toStringAsFixed(1);

  /// Get formatted total earnings
  String get totalEarningsFormatted => '₹${totalEarnings.toStringAsFixed(0)}';

  /// Check if user has any activity
  bool get hasActivity => totalGames > 0;

  /// Get performance level based on win rate
  String get performanceLevel {
    if (winRate >= 80) return 'Legendary';
    if (winRate >= 60) return 'Expert';
    if (winRate >= 40) return 'Advanced';
    if (winRate >= 20) return 'Intermediate';
    return 'Beginner';
  }

  /// Get streak status
  String get streakStatus {
    if (currentStreak == 0) return 'No active streak';
    if (currentStreak == 1) return '1 day streak';
    return '$currentStreak days streak';
  }

  /// Check if current streak is a personal best
  bool get isPersonalBestStreak =>
      currentStreak == longestStreak && currentStreak > 0;

  /// Get stats for profile display
  Map<String, String> get profileStats => {
    'Games': gamesPlayed.toString(),
    'Tournaments': tournamentsJoined.toString(),
    'Wins': wins.toString(),
  };

  /// Get detailed stats for expanded view
  Map<String, String> get detailedStats => {
    'Total Games': totalGames.toString(),
    'Games Played': gamesPlayed.toString(),
    'Tournaments': tournamentsJoined.toString(),
    'Wins': wins.toString(),
    'Win Rate': winRateFormatted,
    'Avg Position': averagePositionFormatted,
    'Total Earnings': totalEarningsFormatted,
    'Current Streak': streakStatus,
    'Longest Streak': longestStreak == 0 ? 'None' : '$longestStreak days',
  };

  @override
  String toString() {
    return 'UserStats(userId: $userId, gamesPlayed: $gamesPlayed, '
        'tournamentsJoined: $tournamentsJoined, wins: $wins, '
        'winRate: $winRate%, totalEarnings: ₹$totalEarnings)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserStats &&
        other.userId == userId &&
        other.gamesPlayed == gamesPlayed &&
        other.tournamentsJoined == tournamentsJoined &&
        other.wins == wins &&
        other.totalEarnings == totalEarnings &&
        other.winRate == winRate &&
        other.averagePosition == averagePosition &&
        other.currentStreak == currentStreak &&
        other.longestStreak == longestStreak;
  }

  @override
  int get hashCode {
    return Object.hash(
      userId,
      gamesPlayed,
      tournamentsJoined,
      wins,
      totalEarnings,
      winRate,
      averagePosition,
      currentStreak,
      longestStreak,
    );
  }
}

/// Default/empty user stats for fallback
class UserStatsDefaults {
  static UserStats get empty {
    final now = DateTime.now();
    return UserStats(
      userId: '',
      gamesPlayed: 0,
      tournamentsJoined: 0,
      wins: 0,
      totalEarnings: 0.0,
      winRate: 0.0,
      averagePosition: 0.0,
      currentStreak: 0,
      longestStreak: 0,
      lastCalculated: now,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Create default stats with current timestamp
  static UserStats createDefault(String userId) {
    final now = DateTime.now();
    return UserStats(
      userId: userId,
      gamesPlayed: 0,
      tournamentsJoined: 0,
      wins: 0,
      totalEarnings: 0.0,
      winRate: 0.0,
      averagePosition: 0.0,
      currentStreak: 0,
      longestStreak: 0,
      lastCalculated: now,
      createdAt: now,
      updatedAt: now,
    );
  }
}
