import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_config.dart';
import '../models/user_stats.dart';
import '../providers/auth_provider.dart';

/// Service for managing user statistics
class UserStatsService {
  static const String _cacheKey = 'user_stats_cache';
  static const String _cacheTimestampKey = 'user_stats_cache_timestamp';
  static const Duration _cacheExpiry = Duration(minutes: 15);

  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 1);
  static const double _backoffMultiplier = 2.0;

  // Debouncing
  Timer? _refreshDebounceTimer;
  static const Duration _debounceDelay = Duration(milliseconds: 500);

  final AuthProvider _authProvider;

  UserStatsService(this._authProvider);

  /// Helper method to perform HTTP requests with exponential backoff retry
  Future<http.Response?> _performRequestWithRetry(
    Future<http.Response> Function() requestFunction,
    String operationName,
  ) async {
    for (int attempt = 0; attempt < _maxRetries; attempt++) {
      try {
        debugPrint('[$operationName] Attempt ${attempt + 1}/$_maxRetries');
        final response = await requestFunction();

        // If successful, return the response
        if (response.statusCode == 200) {
          return response;
        }

        // If it's a 404, don't retry (statistics not found)
        if (response.statusCode == 404) {
          debugPrint(
            '[$operationName] Statistics not found (404), not retrying',
          );
          return response;
        }

        // For other errors, log and potentially retry
        debugPrint(
          '[$operationName] Failed with status ${response.statusCode}: ${response.body}',
        );

        // If this is the last attempt, return the response
        if (attempt == _maxRetries - 1) {
          return response;
        }

        // Calculate delay with exponential backoff
        final delay = Duration(
          milliseconds:
              (_baseDelay.inMilliseconds * pow(_backoffMultiplier, attempt))
                  .round(),
        );

        debugPrint('[$operationName] Retrying in ${delay.inMilliseconds}ms...');
        await Future.delayed(delay);
      } catch (e) {
        debugPrint('[$operationName] Exception on attempt ${attempt + 1}: $e');

        // If this is the last attempt, rethrow the exception
        if (attempt == _maxRetries - 1) {
          rethrow;
        }

        // Calculate delay with exponential backoff
        final delay = Duration(
          milliseconds:
              (_baseDelay.inMilliseconds * pow(_backoffMultiplier, attempt))
                  .round(),
        );

        debugPrint(
          '[$operationName] Retrying in ${delay.inMilliseconds}ms after exception...',
        );
        await Future.delayed(delay);
      }
    }

    return null; // Should never reach here
  }

  /// Get user statistics with caching
  Future<UserStats?> getUserStatistics({bool forceRefresh = false}) async {
    try {
      // Check cache first unless force refresh is requested
      if (!forceRefresh) {
        final cachedStats = await _getCachedStatistics();
        if (cachedStats != null) {
          debugPrint('Returning cached user statistics');
          return cachedStats;
        }
      }

      // Fetch from API with retry logic
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      final response = await _performRequestWithRetry(
        () => http.get(
          Uri.parse('${ApiConfig.baseUrl}/profile/statistics'),
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
        'GetUserStatistics',
      );

      if (response == null) {
        debugPrint('Failed to get response after retries');
        return null;
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final stats = UserStats.fromJson(data['data']);

          // Cache the result
          await _cacheStatistics(stats);

          debugPrint('Successfully fetched user statistics from API');
          return stats;
        }
      } else if (response.statusCode == 404) {
        // Statistics not found, try to trigger calculation with debouncing
        debugPrint('Statistics not found, triggering calculation...');
        await _debouncedRefreshUserStatistics();

        // Try again after calculation (but only once to avoid infinite loops)
        if (!forceRefresh) {
          return await getUserStatistics(forceRefresh: true);
        }
      } else {
        debugPrint('Failed to fetch user statistics: ${response.statusCode}');
        debugPrint('Response: ${response.body}');
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching user statistics: $e');
      return null;
    }
  }

  /// Debounced refresh to prevent multiple simultaneous refresh requests
  Future<void> _debouncedRefreshUserStatistics() async {
    // Cancel any existing timer
    _refreshDebounceTimer?.cancel();

    // Create a new timer
    _refreshDebounceTimer = Timer(_debounceDelay, () async {
      await refreshUserStatistics();
    });
  }

  /// Refresh user statistics (trigger recalculation)
  Future<UserStats?> refreshUserStatistics() async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      final response = await _performRequestWithRetry(
        () => http.post(
          Uri.parse('${ApiConfig.baseUrl}/profile/statistics/refresh'),
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
        'RefreshUserStatistics',
      );

      if (response == null) {
        debugPrint('Failed to get response after retries for refresh');
        return null;
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final stats = UserStats.fromJson(data['data']);

          // Cache the refreshed result
          await _cacheStatistics(stats);

          debugPrint('Successfully refreshed user statistics');
          return stats;
        }
      } else {
        debugPrint('Failed to refresh user statistics: ${response.statusCode}');
        debugPrint('Response: ${response.body}');
      }

      return null;
    } catch (e) {
      debugPrint('Error refreshing user statistics: $e');
      return null;
    }
  }

  /// Get statistics summary for dashboard
  Future<Map<String, dynamic>?> getStatisticsSummary() async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/profile/statistics/summary'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          debugPrint('Successfully fetched statistics summary');
          return data['data'];
        }
      } else {
        debugPrint(
          'Failed to fetch statistics summary: ${response.statusCode}',
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching statistics summary: $e');
      return null;
    }
  }

  /// Update user streak
  Future<bool> updateUserStreak(int currentStreak, int longestStreak) async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return false;
      }

      final response = await http.put(
        Uri.parse('${ApiConfig.baseUrl}/profile/statistics/streak'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'currentStreak': currentStreak,
          'longestStreak': longestStreak,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          debugPrint('Successfully updated user streak');

          // Clear cache to force refresh on next fetch
          await _clearCache();

          return true;
        }
      } else {
        debugPrint('Failed to update user streak: ${response.statusCode}');
      }

      return false;
    } catch (e) {
      debugPrint('Error updating user streak: $e');
      return false;
    }
  }

  /// Get leaderboard for a specific statistic
  Future<List<UserStats>> getLeaderboard(
    String statistic, {
    int limit = 10,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${ApiConfig.baseUrl}/profile/statistics/leaderboard/$statistic?limit=$limit',
        ),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final leaderboard = data['data']['leaderboard'] as List;
          debugPrint('Successfully fetched leaderboard for $statistic');
          return leaderboard.map((item) => UserStats.fromJson(item)).toList();
        }
      } else {
        debugPrint('Failed to fetch leaderboard: ${response.statusCode}');
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching leaderboard: $e');
      return [];
    }
  }

  /// Cache statistics locally
  Future<void> _cacheStatistics(UserStats stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = json.encode(stats.toJson());
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_cacheKey, statsJson);
      await prefs.setInt(_cacheTimestampKey, timestamp);

      debugPrint('Cached user statistics');
    } catch (e) {
      debugPrint('Error caching statistics: $e');
    }
  }

  /// Get cached statistics if not expired
  Future<UserStats?> _getCachedStatistics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_cacheKey);
      final timestamp = prefs.getInt(_cacheTimestampKey);

      if (statsJson != null && timestamp != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
        final cacheExpired = cacheAge > _cacheExpiry.inMilliseconds;

        if (!cacheExpired) {
          final statsData = json.decode(statsJson);
          return UserStats.fromJson(statsData);
        } else {
          debugPrint('Statistics cache expired');
          await _clearCache();
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error reading cached statistics: $e');
      return null;
    }
  }

  /// Clear cached statistics
  Future<void> _clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
      debugPrint('Cleared statistics cache');
    } catch (e) {
      debugPrint('Error clearing statistics cache: $e');
    }
  }

  /// Clear all cached data (for logout)
  Future<void> clearAllCache() async {
    await _clearCache();
  }

  /// Dispose of resources (call when service is no longer needed)
  void dispose() {
    _refreshDebounceTimer?.cancel();
    _refreshDebounceTimer = null;
  }
}
