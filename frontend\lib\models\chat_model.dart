class ChatSession {
  final String id;
  final String userId;
  final String? adminUserId;
  final String sessionType;
  final String status;
  final DateTime startedAt;
  final DateTime? endedAt;
  final DateTime lastActivity;
  final Map<String, dynamic> metadata;
  final User? user;
  final User? admin;
  final int? messageCount;

  ChatSession({
    required this.id,
    required this.userId,
    this.adminUserId,
    required this.sessionType,
    required this.status,
    required this.startedAt,
    this.endedAt,
    required this.lastActivity,
    required this.metadata,
    this.user,
    this.admin,
    this.messageCount,
  });

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      adminUserId: json['admin_user_id'] as String?,
      sessionType: json['session_type'] as String,
      status: json['status'] as String,
      startedAt: DateTime.parse(json['started_at'] as String),
      endedAt: json['ended_at'] != null 
        ? DateTime.parse(json['ended_at'] as String)
        : null,
      lastActivity: DateTime.parse(json['last_activity'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      user: json['user'] != null 
        ? User.fromJson(json['user'] as Map<String, dynamic>)
        : null,
      admin: json['admin'] != null 
        ? User.fromJson(json['admin'] as Map<String, dynamic>)
        : null,
      messageCount: json['message_count'] != null
        ? (json['message_count'] as List).length
        : json['message_count'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'admin_user_id': adminUserId,
      'session_type': sessionType,
      'status': status,
      'started_at': startedAt.toIso8601String(),
      'ended_at': endedAt?.toIso8601String(),
      'last_activity': lastActivity.toIso8601String(),
      'metadata': metadata,
      if (user != null) 'user': user!.toJson(),
      if (admin != null) 'admin': admin!.toJson(),
      if (messageCount != null) 'message_count': messageCount,
    };
  }

  String get sessionTypeLabel {
    switch (sessionType) {
      case 'support':
        return 'Support';
      case 'ai_bot':
        return 'AI Bot';
      default:
        return sessionType;
    }
  }

  String get statusLabel {
    switch (status) {
      case 'active':
        return 'Active';
      case 'waiting':
        return 'Waiting';
      case 'closed':
        return 'Closed';
      case 'transferred':
        return 'Transferred';
      default:
        return status;
    }
  }

  bool get isActive => status == 'active';
  bool get isWaiting => status == 'waiting';
  bool get isClosed => status == 'closed';
}

class ChatMessage {
  final String id;
  final String chatSessionId;
  final String? senderId;
  final String senderType;
  final String messageText;
  final String messageType;
  final bool isRead;
  final DateTime? readAt;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;
  final User? sender;

  ChatMessage({
    required this.id,
    required this.chatSessionId,
    this.senderId,
    required this.senderType,
    required this.messageText,
    required this.messageType,
    required this.isRead,
    this.readAt,
    required this.createdAt,
    required this.metadata,
    this.sender,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      chatSessionId: json['chat_session_id'] as String,
      senderId: json['sender_id'] as String?,
      senderType: json['sender_type'] as String,
      messageText: json['message_text'] as String,
      messageType: json['message_type'] as String,
      isRead: json['is_read'] as bool? ?? false,
      readAt: json['read_at'] != null 
        ? DateTime.parse(json['read_at'] as String)
        : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      sender: json['sender'] != null 
        ? User.fromJson(json['sender'] as Map<String, dynamic>)
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chat_session_id': chatSessionId,
      'sender_id': senderId,
      'sender_type': senderType,
      'message_text': messageText,
      'message_type': messageType,
      'is_read': isRead,
      'read_at': readAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata,
      if (sender != null) 'sender': sender!.toJson(),
    };
  }

  bool get isFromUser => senderType == 'user';
  bool get isFromAdmin => senderType == 'admin';
  bool get isFromSystem => senderType == 'system';
  bool get isFromAiBot => senderType == 'ai_bot';

  String get senderTypeLabel {
    switch (senderType) {
      case 'user':
        return 'User';
      case 'admin':
        return 'Admin';
      case 'system':
        return 'System';
      case 'ai_bot':
        return 'AI Bot';
      default:
        return senderType;
    }
  }

  String get messageTypeLabel {
    switch (messageType) {
      case 'text':
        return 'Text';
      case 'image':
        return 'Image';
      case 'file':
        return 'File';
      case 'system':
        return 'System';
      default:
        return messageType;
    }
  }
}

class User {
  final String id;
  final String name;
  final String email;

  User({
    required this.id,
    required this.name,
    required this.email,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
    };
  }
}
