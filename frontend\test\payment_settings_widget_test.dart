import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wiggyz_app/providers/payment_provider.dart';
import 'package:wiggyz_app/screens/payment_settings_screen.dart';
import 'package:wiggyz_app/screens/settings_screen.dart';
import 'package:wiggyz_app/screens/edit_payment_method_screen.dart';
import 'package:wiggyz_app/theme_provider.dart';

// Mock navigation for testing
class MockNavigatorObserver extends NavigatorObserver {
  List<Route<dynamic>> routes = [];

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    routes.add(route);
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    routes.remove(route);
    super.didPop(route, previousRoute);
  }
}

void main() {
  group('Payment Settings Widget Tests', () {
    late PaymentProvider paymentProvider;
    late ThemeProvider themeProvider;

    setUp(() {
      SharedPreferences.setMockInitialValues({});
      paymentProvider = PaymentProvider();
      themeProvider = ThemeProvider();
    });

    tearDown(() {
      paymentProvider.dispose();
      themeProvider.dispose();
    });

    Widget createTestWidget(Widget child) {
      return MultiProvider(
        providers: [
          ChangeNotifierProvider<PaymentProvider>.value(value: paymentProvider),
          ChangeNotifierProvider<ThemeProvider>.value(value: themeProvider),
        ],
        child: MaterialApp(
          home: child,
          theme: ThemeData.light(),
          darkTheme: ThemeData.dark(),
        ),
      );
    }

    group('SettingsScreen', () {
      testWidgets('should display settings options', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(createTestWidget(const SettingsScreen()));

        // Check if main settings elements are present
        expect(find.text('App Settings'), findsOneWidget);
        expect(find.text('Payment Settings'), findsOneWidget);
        expect(find.text('Notifications'), findsOneWidget);
        expect(find.text('Privacy & Security'), findsOneWidget);
        expect(find.text('Language & Region'), findsOneWidget);
        expect(find.text('Dark Mode'), findsOneWidget);
        expect(find.text('Help & Support'), findsOneWidget);
        expect(find.text('About'), findsOneWidget);
      });

      testWidgets('should navigate to payment settings when tapped', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(createTestWidget(const SettingsScreen()));

        // Find and tap the Payment Settings option
        final paymentSettingsFinder = find.text('Payment Settings');
        expect(paymentSettingsFinder, findsOneWidget);

        await tester.tap(paymentSettingsFinder);
        await tester.pumpAndSettle();

        // Note: In a real test, you would verify navigation occurred
        // This would require setting up proper navigation context
      });

      testWidgets('should toggle dark mode switch', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(createTestWidget(const SettingsScreen()));

        // Find the dark mode switch
        final switchFinder = find.byType(Switch);
        expect(switchFinder, findsOneWidget);

        // Get initial theme mode
        final initialDarkMode = themeProvider.isDarkMode;

        // Tap the switch
        await tester.tap(switchFinder);
        await tester.pump();

        // Verify theme mode changed
        expect(themeProvider.isDarkMode, equals(!initialDarkMode));
      });

      testWidgets(
        'should show coming soon snackbars for unimplemented features',
        (WidgetTester tester) async {
          await tester.pumpWidget(createTestWidget(const SettingsScreen()));

          // Test notifications setting
          await tester.tap(find.text('Notifications'));
          await tester.pumpAndSettle();

          expect(
            find.text('Notification settings coming soon'),
            findsOneWidget,
          );

          // Dismiss snackbar
          await tester.tap(find.byType(SnackBar));
          await tester.pumpAndSettle();

          // Test privacy setting
          await tester.tap(find.text('Privacy & Security'));
          await tester.pumpAndSettle();

          expect(find.text('Privacy settings coming soon'), findsOneWidget);
        },
      );
    });

    group('PaymentSettingsScreen', () {
      testWidgets('should show loading indicator initially', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(const PaymentSettingsScreen()),
        );

        // Should show loading indicator while provider initializes
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should display empty state when no payment methods', (
        WidgetTester tester,
      ) async {
        await paymentProvider.init();
        await tester.pumpWidget(
          createTestWidget(const PaymentSettingsScreen()),
        );
        await tester.pumpAndSettle();

        // Should show empty state
        expect(find.text('No Payment Methods'), findsOneWidget);
        expect(
          find.text('Add your first payment method to get started'),
          findsOneWidget,
        );
      });

      testWidgets('should display payment methods when available', (
        WidgetTester tester,
      ) async {
        // Add a test payment method
        await paymentProvider.init();
        await paymentProvider.addPaymentMethod(
          PaymentMethod(
            type: PaymentType.upi,
            name: 'UPI - test@bank',
            isDefault: true,
            lastUsed: 'Last used today',
          ),
        );

        await tester.pumpWidget(
          createTestWidget(const PaymentSettingsScreen()),
        );
        await tester.pumpAndSettle();

        // Should display the payment method
        expect(find.text('UPI - test@bank'), findsOneWidget);
        expect(find.text('Last used today'), findsOneWidget);
        expect(find.text('Default'), findsOneWidget);
      });

      testWidgets('should display all payment type options', (
        WidgetTester tester,
      ) async {
        await paymentProvider.init();
        await tester.pumpWidget(
          createTestWidget(const PaymentSettingsScreen()),
        );
        await tester.pumpAndSettle();

        // Check if all payment options are displayed
        expect(find.text('UPI'), findsOneWidget);
        expect(find.text('Credit Card'), findsOneWidget);
        expect(find.text('Debit Card'), findsOneWidget);
        expect(find.text('Bank Account'), findsOneWidget);
        expect(find.text('PayPal'), findsOneWidget);
        expect(find.text('Bitcoin'), findsOneWidget);
      });

      testWidgets('should show error state when provider has error', (
        WidgetTester tester,
      ) async {
        await paymentProvider.init();

        // Simulate an error
        paymentProvider.clearError();
        await paymentProvider.updatePaymentMethod(
          PaymentMethod(
            id: 'non-existent',
            type: PaymentType.upi,
            name: 'Test',
            isDefault: false,
            lastUsed: 'Today',
          ),
        );

        await tester.pumpWidget(
          createTestWidget(const PaymentSettingsScreen()),
        );
        await tester.pumpAndSettle();

        // Should show error state
        expect(find.text('Error loading payment methods'), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should retry loading when retry button is tapped', (
        WidgetTester tester,
      ) async {
        await paymentProvider.init();

        // Simulate an error
        await paymentProvider.updatePaymentMethod(
          PaymentMethod(
            id: 'non-existent',
            type: PaymentType.upi,
            name: 'Test',
            isDefault: false,
            lastUsed: 'Today',
          ),
        );

        await tester.pumpWidget(
          createTestWidget(const PaymentSettingsScreen()),
        );
        await tester.pumpAndSettle();

        // Tap retry button
        await tester.tap(find.text('Retry'));
        await tester.pump();

        // Should clear error and show loading
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
    });

    group('EditPaymentMethodScreen', () {
      testWidgets('should display edit form for UPI payment method', (
        WidgetTester tester,
      ) async {
        final testMethod = PaymentMethod(
          id: '1',
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: true,
          lastUsed: 'Last used today',
        );

        await tester.pumpWidget(
          createTestWidget(
            EditPaymentMethodScreen(
              paymentMethod: testMethod,
              onSave: (updatedMethod) {},
            ),
          ),
        );

        // Check if UPI-specific fields are displayed
        expect(find.text('Edit UPI'), findsOneWidget);
        expect(find.text('UPI ID'), findsOneWidget);
        expect(find.text('Name'), findsOneWidget);
        expect(find.text('Save Changes'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should display edit form for Bitcoin payment method', (
        WidgetTester tester,
      ) async {
        final testMethod = PaymentMethod(
          id: '1',
          type: PaymentType.bitcoin,
          name: 'Bitcoin Wallet - bc1q...xyz',
          isDefault: false,
          lastUsed: 'Last used today',
        );

        await tester.pumpWidget(
          createTestWidget(
            EditPaymentMethodScreen(
              paymentMethod: testMethod,
              onSave: (updatedMethod) {},
            ),
          ),
        );

        // Check if Bitcoin-specific fields are displayed
        expect(find.text('Edit Bitcoin'), findsOneWidget);
        expect(find.text('Bitcoin Wallet Address'), findsOneWidget);
        expect(find.text('Wallet Label'), findsOneWidget);
      });

      testWidgets('should display edit form for Credit Card payment method', (
        WidgetTester tester,
      ) async {
        final testMethod = PaymentMethod(
          id: '1',
          type: PaymentType.creditCard,
          name: 'Credit Card ••••1234',
          isDefault: false,
          lastUsed: 'Last used today',
        );

        await tester.pumpWidget(
          createTestWidget(
            EditPaymentMethodScreen(
              paymentMethod: testMethod,
              onSave: (updatedMethod) {},
            ),
          ),
        );

        // Check if Credit Card-specific fields are displayed
        expect(find.text('Edit Credit Card'), findsOneWidget);
        expect(find.text('Card Number'), findsOneWidget);
        expect(find.text('Cardholder Name'), findsOneWidget);
        expect(find.text('Expiry Date'), findsOneWidget);
        expect(find.text('CVV'), findsOneWidget);
      });

      testWidgets('should validate required fields', (
        WidgetTester tester,
      ) async {
        final testMethod = PaymentMethod(
          id: '1',
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: true,
          lastUsed: 'Last used today',
        );

        await tester.pumpWidget(
          createTestWidget(
            EditPaymentMethodScreen(
              paymentMethod: testMethod,
              onSave: (updatedMethod) {},
            ),
          ),
        );

        // Clear the UPI ID field
        final upiIdField = find.byType(TextFormField).first;
        await tester.enterText(upiIdField, '');

        // Try to save
        await tester.tap(find.text('Save Changes'));
        await tester.pump();

        // Should show validation error
        expect(find.text('Please enter upi id'), findsOneWidget);
      });

      testWidgets('should call onSave when form is valid and saved', (
        WidgetTester tester,
      ) async {
        PaymentMethod? savedMethod;
        final testMethod = PaymentMethod(
          id: '1',
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: true,
          lastUsed: 'Last used today',
        );

        await tester.pumpWidget(
          createTestWidget(
            EditPaymentMethodScreen(
              paymentMethod: testMethod,
              onSave: (updatedMethod) {
                savedMethod = updatedMethod;
              },
            ),
          ),
        );

        // Fill in valid data
        final upiIdField = find.byType(TextFormField).first;
        await tester.enterText(upiIdField, 'newemail@bank');

        // Save the form
        await tester.tap(find.text('Save Changes'));
        await tester.pump();

        // Wait for the save operation to complete
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Verify onSave was called with updated method
        expect(savedMethod, isNotNull);
        expect(savedMethod!.name, contains('newemail@bank'));
      });

      testWidgets('should navigate back when cancel is tapped', (
        WidgetTester tester,
      ) async {
        final testMethod = PaymentMethod(
          id: '1',
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: true,
          lastUsed: 'Last used today',
        );

        await tester.pumpWidget(
          createTestWidget(
            EditPaymentMethodScreen(
              paymentMethod: testMethod,
              onSave: (updatedMethod) {},
            ),
          ),
        );

        // Tap cancel button
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Note: In a real test, you would verify navigation occurred
        // This would require setting up proper navigation context
      });
    });
  });
}
