/**
 * Input Sanitization Utilities
 * Provides comprehensive input sanitization and validation for support system
 */

import DOMPurify from 'isomorphic-dompurify';
import { logger } from './logger';

export interface SanitizationOptions {
  allowHtml?: boolean;
  maxLength?: number;
  trimWhitespace?: boolean;
  removeEmojis?: boolean;
  allowedTags?: string[];
  allowedAttributes?: string[];
}

export class InputSanitizer {
  /**
   * Sanitize text input with various options
   */
  static sanitizeText(
    input: string,
    options: SanitizationOptions = {}
  ): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }

    let sanitized = input;

    // Trim whitespace if requested
    if (options.trimWhitespace !== false) {
      sanitized = sanitized.trim();
    }

    // Remove or limit length
    if (options.maxLength && sanitized.length > options.maxLength) {
      sanitized = sanitized.substring(0, options.maxLength);
    }

    // Remove emojis if requested
    if (options.removeEmojis) {
      sanitized = this.removeEmojis(sanitized);
    }

    // Handle HTML content
    if (options.allowHtml) {
      sanitized = this.sanitizeHtml(sanitized, options);
    } else {
      // Escape HTML entities
      sanitized = this.escapeHtml(sanitized);
    }

    // Remove potential SQL injection patterns
    sanitized = this.removeSqlInjectionPatterns(sanitized);

    // Remove potential XSS patterns
    sanitized = this.removeXssPatterns(sanitized);

    return sanitized;
  }

  /**
   * Sanitize HTML content using DOMPurify
   */
  static sanitizeHtml(
    html: string,
    options: SanitizationOptions = {}
  ): string {
    const config: any = {
      ALLOWED_TAGS: options.allowedTags || ['p', 'br', 'strong', 'em', 'u'],
      ALLOWED_ATTR: options.allowedAttributes || [],
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false,
    };

    return DOMPurify.sanitize(html, config) as unknown as string;
  }

  /**
   * Escape HTML entities
   */
  static escapeHtml(text: string): string {
    const htmlEscapes: { [key: string]: string } = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;',
    };

    return text.replace(/[&<>"'/]/g, (match) => htmlEscapes[match]);
  }

  /**
   * Remove emoji characters
   */
  static removeEmojis(text: string): string {
    return text.replace(
      /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
      ''
    );
  }

  /**
   * Remove potential SQL injection patterns
   */
  static removeSqlInjectionPatterns(text: string): string {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /(--|\/\*|\*\/|;|'|"|`)/g,
      /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
      /(\bOR\b|\bAND\b)\s+['"].*['"].*=/gi,
    ];

    let sanitized = text;
    sqlPatterns.forEach((pattern) => {
      sanitized = sanitized.replace(pattern, '');
    });

    return sanitized;
  }

  /**
   * Remove potential XSS patterns
   */
  static removeXssPatterns(text: string): string {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /onload\s*=/gi,
      /onerror\s*=/gi,
      /onclick\s*=/gi,
      /onmouseover\s*=/gi,
    ];

    let sanitized = text;
    xssPatterns.forEach((pattern) => {
      sanitized = sanitized.replace(pattern, '');
    });

    return sanitized;
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  static isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Check for profanity and inappropriate content
   */
  static containsProfanity(text: string): boolean {
    // Basic profanity filter - in production, use a more comprehensive solution
    const profanityWords = [
      'spam', 'scam', 'fraud', 'hack', 'cheat', 'bot',
      // Add more words as needed
    ];

    const lowerText = text.toLowerCase();
    return profanityWords.some(word => lowerText.includes(word));
  }

  /**
   * Detect potential spam content
   */
  static isSpamContent(text: string): boolean {
    const spamIndicators = [
      /(.)\1{4,}/g, // Repeated characters (aaaaa)
      /\b(FREE|URGENT|WINNER|CONGRATULATIONS)\b/gi,
      /\$\d+/g, // Money amounts
      /(http|https):\/\/[^\s]+/gi, // URLs
      /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, // Credit card patterns
    ];

    return spamIndicators.some(pattern => pattern.test(text));
  }

  /**
   * Comprehensive content validation
   */
  static validateContent(
    content: string,
    type: 'message' | 'subject' | 'reply' = 'message'
  ): {
    isValid: boolean;
    errors: string[];
    sanitized: string;
  } {
    const errors: string[] = [];
    let sanitized = content;

    try {
      // Basic validation
      if (!content || typeof content !== 'string') {
        errors.push('Content is required and must be a string');
        return { isValid: false, errors, sanitized: '' };
      }

      // Length validation based on type
      const maxLengths = {
        subject: 200,
        message: 2000,
        reply: 2000,
      };

      const maxLength = maxLengths[type];
      if (content.length > maxLength) {
        errors.push(`Content exceeds maximum length of ${maxLength} characters`);
      }

      // Minimum length validation
      const minLengths = {
        subject: 5,
        message: 10,
        reply: 5,
      };

      const minLength = minLengths[type];
      if (content.trim().length < minLength) {
        errors.push(`Content must be at least ${minLength} characters long`);
      }

      // Sanitize content
      sanitized = this.sanitizeText(content, {
        maxLength,
        trimWhitespace: true,
        allowHtml: false,
      });

      // Check for spam
      if (this.isSpamContent(sanitized)) {
        errors.push('Content appears to be spam');
      }

      // Check for profanity
      if (this.containsProfanity(sanitized)) {
        errors.push('Content contains inappropriate language');
      }

      // Check if content was significantly modified during sanitization
      if (sanitized.length < content.trim().length * 0.5) {
        errors.push('Content contains too many invalid characters');
      }

      return {
        isValid: errors.length === 0,
        errors,
        sanitized,
      };
    } catch (error) {
      logger.error(`Content validation error: ${error}`);
      return {
        isValid: false,
        errors: ['Content validation failed'],
        sanitized: '',
      };
    }
  }

  /**
   * Sanitize support message data
   */
  static sanitizeSupportMessage(data: {
    subject: string;
    category: string;
    message: string;
  }): {
    isValid: boolean;
    errors: string[];
    sanitized: {
      subject: string;
      category: string;
      message: string;
    };
  } {
    const errors: string[] = [];
    const sanitized = {
      subject: '',
      category: '',
      message: '',
    };

    // Validate subject
    const subjectValidation = this.validateContent(data.subject, 'subject');
    if (!subjectValidation.isValid) {
      errors.push(...subjectValidation.errors.map(e => `Subject: ${e}`));
    }
    sanitized.subject = subjectValidation.sanitized;

    // Validate category
    const allowedCategories = [
      'bug_report',
      'feature_request',
      'general_inquiry',
      'account_issue',
      'payment_issue',
      'technical_support',
    ];

    if (!allowedCategories.includes(data.category)) {
      errors.push('Invalid category selected');
    }
    sanitized.category = data.category;

    // Validate message
    const messageValidation = this.validateContent(data.message, 'message');
    if (!messageValidation.isValid) {
      errors.push(...messageValidation.errors.map(e => `Message: ${e}`));
    }
    sanitized.message = messageValidation.sanitized;

    return {
      isValid: errors.length === 0,
      errors,
      sanitized,
    };
  }

  /**
   * Sanitize chat message data
   */
  static sanitizeChatMessage(data: {
    messageText: string;
    messageType?: string;
  }): {
    isValid: boolean;
    errors: string[];
    sanitized: {
      messageText: string;
      messageType: string;
    };
  } {
    const errors: string[] = [];
    const sanitized = {
      messageText: '',
      messageType: data.messageType || 'text',
    };

    // Validate message text
    const messageValidation = this.validateContent(data.messageText, 'message');
    if (!messageValidation.isValid) {
      errors.push(...messageValidation.errors);
    }
    sanitized.messageText = messageValidation.sanitized;

    // Validate message type
    const allowedTypes = ['text', 'image', 'file', 'system'];
    if (!allowedTypes.includes(sanitized.messageType)) {
      errors.push('Invalid message type');
      sanitized.messageType = 'text';
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized,
    };
  }
}
