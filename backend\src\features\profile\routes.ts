import { Router } from 'express';
import { getUserAchievements, updateAchievement } from './controllers/achievementsController';
import { getUserActivity, addActivity } from './controllers/activityController';
import { getUserProfile } from '../auth/controllers/profileController';
import { updateUserProfile } from '../auth/controllers/updateProfileController';
import { authenticate } from '../../middleware/auth';
import { validate } from '../../middleware/validate';
import { activitySchema, achievementUpdateSchema } from './validations';
import { updateProfileSchema } from '../auth/validations';

// Import new controllers
import {
  getUserStatistics,
  refreshUserStatistics,
  getUserStatisticsById,
  getLeaderboard,
  updateUserStreak,
  getStatisticsSummary,
  calculateAllUserStatistics
} from './controllers/userStatsController';

import {
  getUserActivities,
  getRecentActivities,
  getActivitiesByType,
  getActivityStatistics,
  createActivity,
  getUserActivitiesById,
  getAllRecentActivities,
  createMatchWinActivity,
  createAchievementUnlockActivity
} from './controllers/userActivitiesController';

const router = Router();

// Profile routes
router.get('/', authenticate, getUserProfile);
router.put('/', authenticate, validate(updateProfileSchema), updateUserProfile);

// User Statistics routes
router.get('/statistics', authenticate, getUserStatistics);
router.post('/statistics/refresh', authenticate, refreshUserStatistics);
router.get('/statistics/summary', authenticate, getStatisticsSummary);
router.put('/statistics/streak', authenticate, updateUserStreak);
router.get('/statistics/leaderboard/:statistic', getLeaderboard);
router.get('/statistics/:userId', authenticate, getUserStatisticsById);
router.post('/statistics/calculate-all', authenticate, calculateAllUserStatistics);

// User Activities routes
router.get('/activities', authenticate, getUserActivities);
router.get('/activities/recent', authenticate, getRecentActivities);
router.get('/activities/statistics', authenticate, getActivityStatistics);
router.get('/activities/type/:activityType', authenticate, getActivitiesByType);
router.post('/activities', authenticate, createActivity);
router.get('/activities/user/:userId', authenticate, getUserActivitiesById);
router.get('/activities/all/recent', authenticate, getAllRecentActivities);

// Helper activity creation routes
router.post('/activities/match-win', authenticate, createMatchWinActivity);
router.post('/activities/achievement-unlock', authenticate, createAchievementUnlockActivity);

// Legacy Achievement routes (keeping for backward compatibility)
router.get('/achievements', authenticate, getUserAchievements);
router.put('/achievements/:achievementId', authenticate, validate(achievementUpdateSchema), updateAchievement);

// Legacy Activity routes (keeping for backward compatibility)
router.get('/activity', authenticate, getUserActivity);
router.post('/activity', authenticate, validate(activitySchema), addActivity);

export default router;
